import 'dart:developer';
import 'dart:math' as math;
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:startwell/models/meal_model.dart';
import 'package:startwell/models/subscription_plan_model.dart';
import 'package:startwell/models/product_menu_model.dart';
import 'package:startwell/services/meal_selection_manager.dart';
import 'package:startwell/services/subscription_plan_service.dart';
import 'package:startwell/services/product_menu_service.dart';
import 'package:startwell/services/plan_duration_service.dart';
import 'package:startwell/themes/app_theme.dart';
import 'package:startwell/utils/meal_plan_validator.dart';
import 'package:startwell/utils/routes.dart';
import 'package:startwell/widgets/common/info_banner.dart';
import 'package:startwell/widgets/custom_calendar.dart';
import 'package:startwell/screens/manage_student_profile_screen.dart';
import 'package:startwell/widgets/common/veg_icon.dart';
import 'package:startwell/widgets/common/global_app_bar.dart';
import 'package:startwell/widgets/common/gradient_button.dart';
import 'package:startwell/screens/cart_screen.dart';
import 'package:startwell/services/cart_storage_service.dart';
import 'package:startwell/services/end_date_calculation_service.dart';
import '../utils/toast_utils.dart';
// Add these imports for meal selection functionality
import 'package:startwell/utils/meal_constants.dart';
import 'package:startwell/models/user_profile.dart';
import 'package:startwell/services/dropdown_service.dart';
import 'package:startwell/services/working_days_service.dart';

class SubscriptionSelectionScreen extends StatefulWidget {
  final MealSelectionManager selectionManager;
  final List<Meal> selectedMeals;
  final double totalMealCost;
  final int initialPlanIndex;
  final bool isExpressOrder;
  final String mealType;
  // Add optional parameter to support direct navigation without pre-selected meals
  final UserProfile? userProfile;

  const SubscriptionSelectionScreen({
    Key? key,
    required this.selectionManager,
    this.selectedMeals = const [],
    this.totalMealCost = 0.0,
    this.initialPlanIndex = 1,
    this.isExpressOrder = false,
    this.mealType = 'lunch',
    this.userProfile,
  }) : super(key: key);

  @override
  State<SubscriptionSelectionScreen> createState() =>
      _SubscriptionSelectionScreenState();
}

class _SubscriptionSelectionScreenState
    extends State<SubscriptionSelectionScreen> {
  int _selectedPlanIndex = 0;
  bool _isCustomPlan = false;
  bool _showMealChip = false;

  // Initialize all weekdays as unselected (7 days: Mon-Sun)
  late List<bool> _selectedWeekdays;
  final List<String> _weekdayNames = [
    'Mon',
    'Tue',
    'Wed',
    'Thu',
    'Fri',
    'Sat',
    'Sun'
  ];

  late DateTime _startDate;
  late DateTime _firstAvailableDate;
  late DateTime _lastAvailableDate;
  DateTime? _endDate;

  // Track previous focused date to detect month changes
  DateTime? _previousFocusedDate;
  Timer? _focusedDayMonitorTimer;

  // Track the actual calendar's displayed month
  DateTime? _lastKnownCalendarMonth;

  // Alternative approach: Force month change detection
  int _monthChangeCounter = 0;

  List<DateTime> _mealDates = [];
  DateTime? _focusedCalendarDate;

  bool _isMealScheduleExpanded = false;

  // Add meal selection state variables from MealPlanScreen
  List<Map<String, dynamic>> _breakfastMeals = [];
  List<Map<String, dynamic>> _lunchMeals = [];

  // API service
  final ProductMenuService _productMenuService = ProductMenuService();
  bool _isLoadingMeals = true;
  String? _mealLoadError;

  bool isBreakfastDisabled = false;
  bool isLunchDisabled = false;

  // Add state for expanded sections
  bool _isBreakfastExpanded = false;
  bool _isLunchExpanded = false;

  // Store current selected meals for navigation
  List<Meal> _currentSelectedMeals = [];
  double _currentTotalMealCost = 0.0;
  String _currentMealType = '';

  final List<Map<String, dynamic>> _subscriptionPlans = [
    {
      'name': 'Single Day',
      'duration': '1 Day',
      'meals': 1,
      'discount': 0.0,
      'weeks': 0,
      'isSingleDay': true,
      'showPrice': false,
    },
    {
      'name': 'Weekly',
      'duration': '1 Week',
      'meals': 5,
      'discount': 0.0,
      'weeks': 1,
      'showPrice': false,
    },
    {
      'name': 'Monthly',
      'duration': '4 Weeks',
      'meals': 20,
      'discount': 0.0,
      'weeks': 4,
      'showPrice': false,
    },
    {
      'name': 'Quarterly',
      'duration': '3 Months',
      'meals': 60,
      'discount': 0.1,
      'weeks': 12,
      'showPrice': false,
    },
    {
      'name': 'Annual',
      'duration': '12 Months',
      'meals': 200,
      'discount': 0.2,
      'weeks': 48,
      'showPrice': false,
    },
  ];

  String? selectedPlan;
  DateTime? selectedStartDate;
  DateTime? selectedEndDate;
  bool isPreOrder = false;
  DateTime? currentEndDate;

  double _combinedPrice = 0.0;
  double _breakfastPrice = 0.0;

  // Working days API integration
  WorkingDaysResponse? _workingDaysData;
  bool _isLoadingWorkingDays = false;
  Set<DateTime> _enabledDates = {};
  String? _workingDaysError;
  Timer? _workingDaysDebounceTimer;
  double _lunchPrice = 0.0;

  // Plan duration API state
  List<PlanDuration> _planDurations = [];
  bool _isLoadingDurations = false;
  String? _durationLoadError;

  List<Map<String, String>> _deliveryDays = [];
  String _deliveryDaysLabel = '';
  bool _isLoadingDeliveryDays = false;

  // End date calculation API state
  EndDateCalculationResponse? _endDateCalculationResponse;
  bool _isCalculatingEndDate = false;
  String? _endDateCalculationError;

  @override
  void initState() {
    super.initState();
    _selectedPlanIndex = widget.initialPlanIndex;
    _fetchDeliveryDays();

    if (widget.isExpressOrder) {
      _selectedPlanIndex = 0; // Single Day plan
      _isCustomPlan = false; // Regular plan mode
    }

    // Set weekday selection based on plan mode
    if (!_isCustomPlan) {
      // Regular mode - Use default Mon-Fri selection
      _selectedWeekdays =
          EndDateCalculationService.getDefaultWeekdaySelection();
    } else {
      // Custom mode - Start with no days selected
      _selectedWeekdays = List.filled(7, false);
    }

    // Load product menu data
    _loadProductMenu();

    // Load plan durations from API
    _loadPlanDurations();

    // Initialize meal selection if meals were passed from navigation
    if (widget.selectedMeals.isNotEmpty) {
      _currentSelectedMeals = widget.selectedMeals;
      _currentTotalMealCost = widget.totalMealCost;
      _currentMealType = widget.mealType;
    }

    // Enhanced calendar range configuration for comprehensive month navigation
    final now = DateTime.now();

    // Set calendar range to allow 18 months of future planning
    final currentMonthStart = DateTime(now.year, now.month, 1);
    final maxFutureDate =
        DateTime(now.year + 1, now.month + 6, 1); // 18 months ahead

    _firstAvailableDate = currentMonthStart;
    _lastAvailableDate = maxFutureDate;

    // For meal selection, we still need the next weekday as the default start date
    final nextWeekday = _getNextWeekday(now);

    log('📅 Calendar range configured: ${_firstAvailableDate.month}/${_firstAvailableDate.year} to ${_lastAvailableDate.month}/${_lastAvailableDate.year}');

    // For express orders, calculate the appropriate day based on express window
    if (widget.isExpressOrder) {
      final bool isExpressWindowOpen = isWithinExpressWindow();

      // If express window is open and it's a weekday, use today
      if (isExpressWindowOpen && now.weekday <= 5) {
        _startDate = DateTime(now.year, now.month, now.day);
      } else {
        // Otherwise use the next weekday
        _startDate = nextWeekday;
      }
      _focusedCalendarDate = _startDate;
      _calculateMealDates();
    } else {
      // For regular orders, don't set a default start date
      _startDate = _firstAvailableDate;
      _focusedCalendarDate = _firstAvailableDate;
      if (_isCustomPlan) {
        _calculateMealDates();
      }
    }

    // Load working days for calendar
    _loadWorkingDays();

    // Custom calendar handles month navigation directly - no monitoring needed
    log('📅 🚀 Custom Calendar initialized - direct month navigation enabled');
  }

  Future<void> _fetchDeliveryDays() async {
    setState(() {
      _isLoadingDeliveryDays = true;
    });
    try {
      final dropdowns = await DropdownService.fetchDropdowns();
      final days = dropdowns['delivery_days'] ?? [];
      setState(() {
        _deliveryDays = days;
        if (days.isNotEmpty) {
          final first = days.first['short_name'] ?? '';
          final last = days.last['short_name'] ?? '';
          _deliveryDaysLabel = first == last ? first : '$first to $last';
        } else {
          _deliveryDaysLabel = '';
        }
        // Always re-initialize to match 7-day week structure
        _selectedWeekdays = List.filled(7, false);
        // Set default Mon-Fri selection for regular mode
        if (!_isCustomPlan) {
          _selectedWeekdays =
              EndDateCalculationService.getDefaultWeekdaySelection();
        }
        _isLoadingDeliveryDays = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingDeliveryDays = false;
      });
    }
  }

  /// Debounced version of _loadWorkingDays to prevent rapid API calls
  void _loadWorkingDaysDebounced(
      {Duration delay = const Duration(milliseconds: 300)}) {
    // Cancel any existing timer
    _workingDaysDebounceTimer?.cancel();

    // Set loading state immediately for better UX
    if (!_isLoadingWorkingDays) {
      setState(() {
        _isLoadingWorkingDays = true;
        _workingDaysError = null;
      });
    }

    // Start new timer
    _workingDaysDebounceTimer = Timer(delay, () {
      _loadWorkingDays();
    });
  }

  /// Load working days from API based on meal type and selected custom days
  /// Uses the currently focused calendar month for dynamic month navigation
  Future<void> _loadWorkingDays() async {
    log('🚀 Starting _loadWorkingDays...');

    // Use the currently focused calendar month, or current month as fallback
    final DateTime targetMonth = _focusedCalendarDate ?? DateTime.now();
    log('📅 Loading working days for focused month: ${targetMonth.month}/${targetMonth.year}');

    setState(() {
      _isLoadingWorkingDays = true;
      _workingDaysError = null;
    });

    try {
      // Get custom days based on current mode
      List<int>? customDays;
      if (_isCustomPlan) {
        customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(
            _selectedWeekdays);
        log('🗓️ Loading working days for custom days: $customDays');
      } else {
        // Use default delivery days for regular mode
        customDays = WorkingDaysService.getDefaultDeliveryDays();
        log('🗓️ Using default delivery days: $customDays');
      }

      // Determine meal type based on current context
      String mealType = widget.mealType;
      if (mealType.isEmpty) {
        mealType = 'lunch'; // Default to lunch
      }

      log('🗓️ Loading working days for meal type: $mealType');
      log('🗓️ Custom days: $customDays');
      log('🗓️ Is custom plan: $_isCustomPlan');
      log('🗓️ Selected weekdays: $_selectedWeekdays');

      // Validate API configuration first
      final isValidConfig = WorkingDaysService.validateApiConfiguration();
      log('🔧 API configuration valid: $isValidConfig');

      // Load working days for the focused month (dynamic month navigation)
      log('📡 Calling getWorkingDays for focused month...');
      final workingDaysResponse = await WorkingDaysService.getWorkingDays(
        mealType: mealType,
        monthSelected: targetMonth,
        customDays: customDays,
      );
      log('📡 Focused month response: success=${workingDaysResponse.success}, dates=${workingDaysResponse.enabledDates.length}');

      // Also load next month for better UX (if within allowed range)
      WorkingDaysResponse? nextMonthResponse;
      final nextMonth = DateTime(targetMonth.year, targetMonth.month + 1, 1);

      if (!nextMonth.isAfter(_lastAvailableDate)) {
        log('📡 Loading next month ${nextMonth.month}/${nextMonth.year} for better UX...');
        nextMonthResponse = await WorkingDaysService.getWorkingDays(
          mealType: mealType,
          monthSelected: nextMonth,
          customDays: customDays,
        );
        log('📡 Next month response: success=${nextMonthResponse.success}, dates=${nextMonthResponse.enabledDates.length}');
      } else {
        log('📡 Skipping next month ${nextMonth.month}/${nextMonth.year} - outside allowed range');
      }

      setState(() {
        _workingDaysData = workingDaysResponse;
        _enabledDates = {
          ...workingDaysResponse.enabledDates,
          if (nextMonthResponse != null) ...nextMonthResponse.enabledDates,
        };
        _isLoadingWorkingDays = false;
      });

      log('✅ Working days loaded successfully: ${_enabledDates.length} enabled dates');
      log('📅 Enabled dates: ${_enabledDates.map((d) => "${d.day}/${d.month}/${d.year}").join(", ")}');

      // If no dates were loaded, log the API responses for debugging
      if (_enabledDates.isEmpty) {
        log('⚠️ No enabled dates loaded! API responses:');
        log('Focused month: ${workingDaysResponse.message}');
        if (nextMonthResponse != null) {
          log('Next month: ${nextMonthResponse.message}');
        }
        log('Focused month metadata: ${workingDaysResponse.metadata}');
        if (nextMonthResponse != null) {
          log('Next month metadata: ${nextMonthResponse.metadata}');
        }
      }
    } catch (e, stackTrace) {
      log('❌ Error loading working days: $e');
      log('❌ Stack trace: $stackTrace');
      setState(() {
        _workingDaysError = e.toString();
        _isLoadingWorkingDays = false;
        // Fallback to weekdays only
        _enabledDates = _generateWeekdayFallback();
      });
      log('🔄 Using fallback dates: ${_enabledDates.length} dates');
    }
  }

  /// Generate fallback enabled dates (weekdays only) if API fails
  Set<DateTime> _generateWeekdayFallback() {
    final Set<DateTime> fallbackDates = {};
    final now = DateTime.now();

    // Generate weekdays for current and next month
    for (int month = 0; month <= 1; month++) {
      final targetMonth = DateTime(now.year, now.month + month, 1);
      final daysInMonth =
          DateTime(targetMonth.year, targetMonth.month + 1, 0).day;

      for (int day = 1; day <= daysInMonth; day++) {
        final date = DateTime(targetMonth.year, targetMonth.month, day);
        // Only include weekdays (Monday = 1, Friday = 5)
        if (date.weekday >= 1 && date.weekday <= 5) {
          fallbackDates.add(date);
        }
      }
    }

    log('🔄 Generated ${fallbackDates.length} fallback weekday dates');
    return fallbackDates;
  }

  // Old monitoring system removed - Custom calendar handles navigation directly

  /// Enhanced monitoring for arrow button navigation
  void _monitorFocusedDayChanges() {
    // Get the current calendar state
    final currentFocused = _ensureValidFocusedDay();
    final now = DateTime.now();

    // Debug: Log current state every few seconds (reduce spam)
    if (now.millisecondsSinceEpoch % 2000 < 500) {
      log('🔍 Enhanced Monitoring:');
      log('   📅 _focusedCalendarDate: ${_focusedCalendarDate?.month}/${_focusedCalendarDate?.year}');
      log('   📅 currentFocused: ${currentFocused.month}/${currentFocused.year}');
      log('   📅 _lastKnownCalendarMonth: ${_lastKnownCalendarMonth?.month}/${_lastKnownCalendarMonth?.year}');
    }

    // Initialize tracking variables
    if (_previousFocusedDate == null) {
      log('📅 Initializing month tracking:');
      log('   📅 Setting _previousFocusedDate: ${currentFocused.month}/${currentFocused.year}');
      log('   📅 Setting _lastKnownCalendarMonth: ${currentFocused.month}/${currentFocused.year}');
      _previousFocusedDate = currentFocused;
      _lastKnownCalendarMonth = currentFocused;
      return;
    }

    // Check for month changes using multiple detection methods
    final focusedMonthChanged =
        _previousFocusedDate!.month != currentFocused.month ||
            _previousFocusedDate!.year != currentFocused.year;

    final calendarMonthChanged = _lastKnownCalendarMonth != null &&
        (_lastKnownCalendarMonth!.month != currentFocused.month ||
            _lastKnownCalendarMonth!.year != currentFocused.year);

    if (focusedMonthChanged || calendarMonthChanged) {
      log('📅 🚀 MONTH CHANGE DETECTED!');
      log('   📅 Detection method: ${focusedMonthChanged ? "FocusedDate" : "CalendarState"}');
      log('   📅 Previous: ${_previousFocusedDate!.month}/${_previousFocusedDate!.year}');
      log('   📅 Current: ${currentFocused.month}/${currentFocused.year}');

      _previousFocusedDate = currentFocused;
      _lastKnownCalendarMonth = currentFocused;
      _handleMonthNavigation(currentFocused, 'EnhancedMonitoring');
    }
  }

  /// Debug method to handle month navigation from any source
  Future<void> _handleMonthNavigation(
      DateTime focusedMonth, String source) async {
    log('📅 🚀 Month navigation triggered from: $source');
    log('📅 Target month: ${focusedMonth.month}/${focusedMonth.year}');
    log('📅 Current focused month: ${_focusedCalendarDate?.month}/${_focusedCalendarDate?.year}');

    // Check if this is actually a month change
    final isMonthChange = _focusedCalendarDate == null ||
        _focusedCalendarDate!.month != focusedMonth.month ||
        _focusedCalendarDate!.year != focusedMonth.year;

    if (!isMonthChange) {
      log('⏭️ Same month - no API call needed');
      return;
    }

    log('✅ Month change detected - proceeding with API call');
    await _loadWorkingDaysForMonth(focusedMonth);

    // Update focused calendar date after successful API call
    setState(() {
      _focusedCalendarDate = focusedMonth;
    });
    log('📅 ✅ Updated _focusedCalendarDate to: ${_focusedCalendarDate?.month}/${_focusedCalendarDate?.year}');
  }

  /// Comprehensive month navigation API integration
  /// Handles any month within the calendar range with real-time API calls
  Future<void> _loadWorkingDaysForMonth(DateTime focusedMonth) async {
    print(
        '🔥🔥🔥 _loadWorkingDaysForMonth CALLED for ${focusedMonth.month}/${focusedMonth.year} 🔥🔥🔥');
    log('📅 🚀 Starting comprehensive month navigation for: ${focusedMonth.month}/${focusedMonth.year}');
    log('🔍 Method called from: ${StackTrace.current.toString().split('\n')[1]}');
    log('🌐 *** API CALL WILL BE TRIGGERED FOR MONTH: ${focusedMonth.month}/${focusedMonth.year} ***');
    print(
        '🔥🔥🔥 *** API CALL WILL BE TRIGGERED FOR MONTH: ${focusedMonth.month}/${focusedMonth.year} *** 🔥🔥🔥');

    // Validate month is within allowed range
    if (focusedMonth.isBefore(_firstAvailableDate) ||
        focusedMonth.isAfter(_lastAvailableDate)) {
      log('⚠️ Month ${focusedMonth.month}/${focusedMonth.year} is outside allowed range');
      return;
    }

    // Set loading state for better UX and clear existing dates to force calendar update
    setState(() {
      _isLoadingWorkingDays = true;
      _workingDaysError = null;
      // Clear enabled dates to show loading state in calendar
      _enabledDates = {};
    });

    log('🔄 Loading state activated, calendar cleared for month: ${focusedMonth.month}/${focusedMonth.year}');

    try {
      // Get custom days based on current mode
      List<int>? customDays;
      if (_isCustomPlan) {
        customDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(
            _selectedWeekdays);
        log('🗓️ Custom days for month change: $customDays');
      } else {
        // Use default delivery days for regular mode
        customDays = WorkingDaysService.getDefaultDeliveryDays();
        log('🗓️ Using default delivery days for month change: $customDays');
      }

      // Determine meal type based on current context
      String mealType = widget.mealType;
      if (mealType.isEmpty) {
        mealType = 'lunch'; // Default to lunch
      }

      log('🔄 🌐 Comprehensive month navigation API call:');
      log('   📅 Target Month: ${focusedMonth.month}/${focusedMonth.year}');
      log('   🍽️ Meal Type: $mealType');
      log('   📋 Custom Days: $customDays');
      log('   🔗 API Endpoint: http://192.168.1.167:8005/api/v2/admin/settings/working-days');

      // Load working days for the focused month with comprehensive error handling
      print('🔥🔥🔥 MAKING ACTUAL API CALL NOW! 🔥🔥🔥');
      final monthResponse = await WorkingDaysService.getWorkingDays(
        mealType: mealType,
        monthSelected: focusedMonth,
        customDays: customDays,
      );
      print(
          '🔥🔥🔥 API CALL COMPLETED! Success: ${monthResponse.success} 🔥🔥🔥');

      // Optimized next month loading - only load if within range and beneficial for UX
      WorkingDaysResponse? nextMonthResponse;
      final nextMonth = DateTime(focusedMonth.year, focusedMonth.month + 1, 1);

      if (!nextMonth.isAfter(_lastAvailableDate)) {
        log('📡 Loading next month ${nextMonth.month}/${nextMonth.year} for better UX...');
        nextMonthResponse = await WorkingDaysService.getWorkingDays(
          mealType: mealType,
          monthSelected: nextMonth,
          customDays: customDays,
        );
      } else {
        log('📡 Skipping next month ${nextMonth.month}/${nextMonth.year} - outside allowed range');
      }

      log('✅ API Response Summary:');
      log('   📅 Month ${focusedMonth.month}/${focusedMonth.year}: success=${monthResponse.success}, dates=${monthResponse.enabledDates.length}');
      if (nextMonthResponse != null) {
        log('   📅 Next month ${nextMonth.month}/${nextMonth.year}: success=${nextMonthResponse.success}, dates=${nextMonthResponse.enabledDates.length}');
      }

      setState(() {
        _workingDaysData = monthResponse;
        _enabledDates = {
          ...monthResponse.enabledDates,
          if (nextMonthResponse != null) ...nextMonthResponse.enabledDates,
        };
        _isLoadingWorkingDays = false;
      });

      print(
          '🔥🔥🔥 _enabledDates UPDATED! Total dates: ${_enabledDates.length} 🔥🔥🔥');
      print('🔥 Sample enabled dates: ${_enabledDates.take(5).toList()}');

      final totalEnabledDates = _enabledDates.length;
      log('🎉 ✅ Month navigation completed successfully!');
      log('   📅 Target Month: ${focusedMonth.month}/${focusedMonth.year}');
      log('   📊 Total Enabled Dates: $totalEnabledDates');
      log('   📋 Sample Dates: ${_enabledDates.take(5).map((d) => "${d.day}/${d.month}/${d.year}").join(", ")}${_enabledDates.length > 5 ? "..." : ""}');
      log('   🔄 Calendar will now update with new enabled dates');
    } catch (e, stackTrace) {
      log('❌ 🚨 Comprehensive month navigation failed!');
      log('   📅 Target Month: ${focusedMonth.month}/${focusedMonth.year}');
      log('   ❌ Error: $e');
      log('   📋 Stack Trace: $stackTrace');

      setState(() {
        _workingDaysError =
            'Failed to load working days for ${focusedMonth.month}/${focusedMonth.year}: $e';
        _isLoadingWorkingDays = false;
        // Provide fallback dates to ensure calendar remains functional
        _enabledDates = _generateWeekdayFallback();
      });

      log('🔄 Fallback applied: ${_enabledDates.length} weekday dates generated');
    }
  }

  /// Calculate end date using the backend API
  Future<void> _calculateEndDateWithAPI() async {
    // Only calculate if we have all required data
    if (_selectedPlanIndex < 0 ||
        _planDurations.isEmpty ||
        _selectedPlanIndex >= _planDurations.length) {
      log('⚠️ Cannot calculate end date: invalid plan selection');
      return;
    }

    final selectedPlan = _planDurations[_selectedPlanIndex];
    final planId = selectedPlan.id;

    // Get selected days based on current mode
    List<int> selectedDays;
    if (_isCustomPlan) {
      selectedDays = EndDateCalculationService.convertSelectedWeekdaysToDays(
          _selectedWeekdays);
    } else {
      // Regular plan uses default Monday to Friday (1-5)
      selectedDays = EndDateCalculationService.getDefaultDeliveryDays();
    }

    // Skip calculation if no days selected
    if (selectedDays.isEmpty) {
      log('⚠️ Cannot calculate end date: no days selected');
      return;
    }

    setState(() {
      _isCalculatingEndDate = true;
      _endDateCalculationError = null;
    });

    try {
      log('🗓️ Calculating end date with API...');
      log('   📅 Start Date: ${DateFormat('yyyy-MM-dd').format(_startDate)}');
      log('   📋 Plan ID: $planId');
      log('   📅 Selected Days: $selectedDays');

      final response = await EndDateCalculationService.calculateEndDate(
        startDate: _startDate,
        planId: planId,
        selectedDays: selectedDays,
      );

      setState(() {
        _endDateCalculationResponse = response;
        _endDate = response.endDate; // Update the existing _endDate variable
        _isCalculatingEndDate = false;
      });

      log('✅ End date calculated successfully: ${DateFormat('yyyy-MM-dd').format(response.endDate)}');

      // Recalculate meal dates with the new end date
      _calculateMealDates();
    } catch (e) {
      log('❌ End date calculation failed: $e');

      setState(() {
        _endDateCalculationError = _getErrorMessage(e);
        _isCalculatingEndDate = false;
        // Keep the existing local calculation as fallback
      });

      // Show user-friendly error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Using local calculation: ${_getErrorMessage(e)}'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    // Clean up the debounce timer
    _workingDaysDebounceTimer?.cancel();
    // Clean up the focused day monitoring timer
    _focusedDayMonitorTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if this is a pre-order
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      isPreOrder = args['isPreOrder'] ?? false;
      currentEndDate = args['currentEndDate'] as DateTime?;
    }
  }

  // Get plan data from API or fallback to hardcoded data
  Map<String, dynamic> _getPlanData(int index) {
    if (_planDurations.isNotEmpty && index < _planDurations.length) {
      final duration = _planDurations[index];
      return {
        'name': duration.planName,
        'duration': _getDurationDisplay(duration.planQuantity),
        'meals': duration.planQuantity,
        'discount': duration.discountPercentage ?? 0.0,
        'weeks': _calculateWeeks(duration.planQuantity),
        'isSingleDay': duration.planQuantity == 1,
        'showPrice': false,
      };
    }
    // Fallback to hardcoded data if API fails
    return _subscriptionPlans[index];
  }

  // Calculate weeks from plan quantity
  int _calculateWeeks(int planQuantity) {
    if (planQuantity == 1) return 0; // Single day
    if (planQuantity <= 5) return 1; // Weekly
    if (planQuantity <= 20) return 4; // Monthly
    if (planQuantity <= 60) return 12; // Quarterly
    return 48; // Annual
  }

  // Get duration display text
  String _getDurationDisplay(int planQuantity) {
    if (planQuantity == 1) return '1 Day';
    if (planQuantity <= 5) return '1 Week';
    if (planQuantity <= 20) return '4 Weeks';
    if (planQuantity <= 60) return '3 Months';
    return '12 Months';
  }

  // Build plan duration loading section
  Widget _buildPlanDurationLoadingSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double screenWidth = constraints.maxWidth;

        // Responsive grid configuration
        int crossAxisCount;
        double childAspectRatio;
        double horizontalPadding;

        if (screenWidth < 400) {
          crossAxisCount = 2;
          childAspectRatio = 0.85;
          horizontalPadding = 8.0;
        } else if (screenWidth < 600) {
          crossAxisCount = 3;
          childAspectRatio = 0.9;
          horizontalPadding = 12.0;
        } else {
          crossAxisCount = 3;
          childAspectRatio = 1.0;
          horizontalPadding = 16.0;
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: childAspectRatio,
              mainAxisSpacing: 12.0,
              crossAxisSpacing: 12.0,
            ),
            itemCount: 6, // Show 6 loading cards
            itemBuilder: (context, index) {
              return Container(
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 60,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: 40,
                      height: 10,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  // Build plan duration error section
  Widget _buildPlanDurationErrorSection(String error) {
    return Container(
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange[700],
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Failed to load plan durations',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.orange[700],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Using default plans. Tap retry to try again.',
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: Colors.orange[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadPlanDurations,
            icon: const Icon(Icons.refresh, size: 18),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ],
      ),
    );
  }

  // Load plan durations from API
  Future<void> _loadPlanDurations() async {
    try {
      setState(() {
        _isLoadingDurations = true;
        _durationLoadError = null;
      });

      final durations = await PlanDurationService.fetchDurations();

      setState(() {
        _planDurations = durations;
        _isLoadingDurations = false;
      });

      print('Plan durations loaded: ${durations.length} durations');
    } catch (e) {
      setState(() {
        _isLoadingDurations = false;
        _durationLoadError = e.toString();
      });
      print('Error loading plan durations: $e');
    }
  }

  // Load product menu from API
  Future<void> _loadProductMenu() async {
    try {
      _isLoadingMeals = true;
      _mealLoadError = null;

      final response = await _productMenuService.getProductMenu();

      // Convert API products to UI format
      final breakfastProducts =
          _productMenuService.getBreakfastProducts(response);
      final lunchProducts = _productMenuService.getLunchProducts(response);

      _breakfastMeals = breakfastProducts
          .map((product) => _productMenuService.productToMap(product))
          .toList();
      _lunchMeals = lunchProducts
          .map((product) => _productMenuService.productToMap(product))
          .toList();

      // Pre-select meals if they were passed from navigation
      if (widget.selectedMeals.isNotEmpty) {
        for (var meal in widget.selectedMeals) {
          if (meal.categories.contains(MealCategory.breakfast)) {
            for (var breakfastMeal in _breakfastMeals) {
              if (breakfastMeal['name'] == meal.name) {
                breakfastMeal['isSelected'] = true;
                break;
              }
            }
          }
          if (meal.categories.contains(MealCategory.lunch)) {
            for (var lunchMeal in _lunchMeals) {
              if (lunchMeal['name'] == meal.name) {
                lunchMeal['isSelected'] = true;
                break;
              }
            }
          }
        }
      }

      setState(() {
        _isLoadingMeals = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMeals = false;
        _mealLoadError = e.toString();
      });
      log('Error loading product menu: $e');
    }
  }

  // Check if start date has been manually selected
  bool _isStartDateSelected() {
    if (widget.isExpressOrder) return true;
    if (_isCustomPlan) return true;
    return _startDate != _firstAvailableDate;
  }

  // Get currently selected meals from UI
  List<Meal> _getCurrentlySelectedMeals() {
    List<Meal> selectedMeals = [];

    // Check selected breakfast meals
    for (var breakfastMeal in _breakfastMeals) {
      if (breakfastMeal['isSelected']) {
        selectedMeals.add(Meal(
          id: 'breakfast_${breakfastMeal['name'].toString().toLowerCase().replaceAll(' ', '_')}',
          name: breakfastMeal['name'],
          description: 'Nutritious breakfast option',
          price: breakfastMeal['price'] as double,
          type: MealType.veg, // Default to veg
          categories: [MealCategory.breakfast],
          imageUrl: '',
          ingredients: ['Fresh ingredients', 'Nutritional supplements'],
          nutritionalInfo: {'calories': '250', 'protein': '15g'},
          allergyInfo: ['May contain gluten'],
        ));
      }
    }

    // Check selected lunch meals
    for (var lunchMeal in _lunchMeals) {
      if (lunchMeal['isSelected']) {
        selectedMeals.add(Meal(
          id: 'lunch_${lunchMeal['name'].toString().toLowerCase().replaceAll(' ', '_')}',
          name: lunchMeal['name'],
          description: 'Delicious lunch option',
          price: lunchMeal['price'] as double,
          type: MealType.veg, // Default to veg
          categories: [MealCategory.lunch],
          imageUrl: '',
          ingredients: ['Fresh vegetables', 'Quality grains'],
          nutritionalInfo: {'calories': '400', 'protein': '20g'},
          allergyInfo: ['May contain gluten'],
        ));
      }
    }

    return selectedMeals;
  }

  // Get current meal type based on selected meals
  String _getCurrentMealType() {
    bool hasBreakfast = _breakfastMeals.any((meal) => meal['isSelected']);
    bool hasLunch = _lunchMeals.any((meal) => meal['isSelected']);

    if (hasBreakfast && hasLunch) {
      return 'breakfast_and_lunch';
    } else if (hasBreakfast) {
      return 'breakfast';
    } else if (hasLunch) {
      return 'lunch';
    } else {
      return widget.mealType; // fallback to original meal type
    }
  }

  // Modified _navigateToOrderSummary to go to cart first
  void _navigateToOrderSummary(BuildContext context) {
    if (!_isStartDateSelected()) {
      ToastUtils.showToast(
        context: context,
        message: 'Please select a start date to begin your subscription plan',
        type: ToastType.error,
      );
      return;
    }

    // Check if at least one meal is selected
    List<Meal> currentSelectedMeals = _getCurrentlySelectedMeals();
    if (currentSelectedMeals.isEmpty) {
      ToastUtils.showToast(
        context: context,
        message:
            'Please select at least one meal option (Breakfast or Lunch) to continue with your subscription',
        type: ToastType.error,
      );
      return;
    }

    log("endDate: $_endDate");
    log("startDate: $_startDate");
    log("mealDates: $_mealDates");
    log("selectedWeekdays: $_selectedWeekdays");
    log("selectedPlanIndex: $_selectedPlanIndex");
    log("isCustomPlan: $_isCustomPlan");
    log("isExpressOrder: ${widget.isExpressOrder}");
    log("currentSelectedMeals: ${currentSelectedMeals.map((m) => m.name).toList()}");

    // Save the selected subscription plan
    _saveSubscriptionPlan();

    // Get current meal type based on selection
    String currentMealType = _getCurrentMealType();

    // Handle cart update if this is the same meal type - remove existing cart item first
    if (MealSelectionManager.hasBreakfastInCart &&
            (currentMealType == 'breakfast' ||
                currentMealType == 'breakfast_and_lunch') ||
        MealSelectionManager.hasLunchInCart &&
            (currentMealType == 'lunch' ||
                currentMealType == 'breakfast_and_lunch')) {
      // If handling both meal types at once, clear both from cart
      if (currentMealType == 'breakfast_and_lunch') {
        if (MealSelectionManager.hasBreakfastInCart) {
          CartStorageService.removeCartItemsByMealType('breakfast');
        }
        if (MealSelectionManager.hasLunchInCart) {
          CartStorageService.removeCartItemsByMealType('lunch');
        }
      } else {
        // Clear any existing cart items of the same meal type before adding the updated one
        CartStorageService.removeCartItemsByMealType(currentMealType);
      }
    }

    // Calculate combined price for both breakfast and lunch if needed
    final totalAmount = _getCombinedPrice();

    // Navigate to the Cart screen instead of directly to student profile
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CartScreen(
          planType: _getPlanData(_selectedPlanIndex)['name'],
          isCustomPlan: _isCustomPlan,
          selectedWeekdays: _selectedWeekdays,
          startDate: _startDate,
          endDate: _endDate!,
          mealDates: _mealDates,
          totalAmount: totalAmount,
          selectedMeals:
              currentSelectedMeals, // Use currently selected meals from UI
          isExpressOrder: widget.isExpressOrder,
          mealType: currentMealType, // Use dynamically determined meal type
          // Pass individual prices for breakfast and lunch when both are selected
          breakfastPrice:
              currentMealType == 'breakfast_and_lunch' ? _breakfastPrice : null,
          lunchPrice:
              currentMealType == 'breakfast_and_lunch' ? _lunchPrice : null,
        ),
      ),
    );
  }

  // Save the selected subscription plan
  void _saveSubscriptionPlan() {
    final String planType = _getPlanData(_selectedPlanIndex)['name'];
    dynamic deliveryMode;

    if (_isCustomPlan) {
      // Custom delivery mode (specific days)
      deliveryMode = "Custom Days";
    } else {
      // Standard delivery mode (Mon to Fri)
      deliveryMode = "Weekdays Only";
    }

    // Create subscription plan model
    final SubscriptionPlanModel plan = SubscriptionPlanModel(
      planType: planType,
      deliveryMode: deliveryMode,
      startDate: _startDate,
      endDate: _endDate!,
    );

    // Save to SharedPreferences
    SubscriptionPlanService.saveSubscriptionPlan(plan);
  }

  // Modified _handlePlanModeToggle to handle chip visibility
  void _handlePlanModeToggle(bool isCustom) {
    setState(() {
      _isCustomPlan = isCustom;
      _showMealChip = false;

      if (!isCustom) {
        // Regular mode (Mon to Fri) - Use default weekday selection
        _selectedWeekdays =
            EndDateCalculationService.getDefaultWeekdaySelection();
        // Reset start date to first available date
        _startDate = _firstAvailableDate;
      } else {
        // Custom mode - Unselect all weekdays
        _selectedWeekdays = List.filled(7, false);
        // For custom mode, find the earliest date based on selected weekdays
        _startDate = calculateCustomPlanStartDate(
          selectedStartDate: _startDate,
          selectedWeekdays: _getSelectedWeekdayIndexes(),
        );
      }

      _focusedCalendarDate = _startDate;
      _calculateMealDates();
      _showMealChip = _shouldShowMealChip();
    });

    // Reload working days when plan mode changes
    _loadWorkingDays();
  }

  // Get next two weekdays for Regular plan
  List<DateTime> _getNextTwoWeekdays(DateTime start) {
    final days = <DateTime>[];
    DateTime current = start;
    while (days.length < 2) {
      if (current.weekday <= 5) days.add(current);
      current = current.add(const Duration(days: 1));
    }
    return days;
  }

  // Get next two meal dates for Custom plan
  List<DateTime> _getCustomUpcomingDates(
      DateTime start, List<int> selectedWeekdays) {
    final result = <DateTime>[];
    DateTime current = start;

    // If no weekdays selected, return empty list
    if (selectedWeekdays.isEmpty) {
      return result;
    }

    // Try for up to 14 days to find 2 matching dates
    int daysChecked = 0;
    while (result.length < 2 && daysChecked < 14) {
      if (selectedWeekdays.contains(current.weekday)) {
        result.add(current);
      }
      current = current.add(const Duration(days: 1));
      daysChecked++;
    }
    return result;
  }

  // Get upcoming meal dates based on plan type
  List<DateTime> _getUpcomingMealDates() {
    // For Express 1-Day or Single Day plan, show only one date
    if (_selectedPlanIndex == 0 || widget.isExpressOrder) {
      return [_startDate];
    }

    // For Custom plan
    if (_isCustomPlan) {
      return _getCustomUpcomingDates(_startDate, _getSelectedWeekdayIndexes());
    }

    // For Regular plan (Weekly, Monthly, etc.)
    return _getNextTwoWeekdays(_startDate);
  }

  // Get meal item text based on meal type
  String _getMealItemsText() {
    return widget.mealType == 'breakfast'
        ? 'Breakfast Item 1, Breakfast Item 2, Seasonal Fruits'
        : 'Lunch Item 1, Lunch Item 2, Salad';
  }

  // Check if current time is within Express window (12:00 AM to 8:00 AM IST)
  bool isWithinExpressWindow() {
    // Convert to IST time (UTC + 5:30)
    DateTime now =
        DateTime.now().toUtc().add(const Duration(hours: 5, minutes: 30));
    final nowHour = now.hour;
    return nowHour >= 0 && nowHour < 8;
  }

  // Find the next weekday (Mon-Fri) from a given date
  DateTime _getNextWeekday(DateTime date) {
    DateTime nextDate = date.add(const Duration(days: 1));
    while (nextDate.weekday > 5) {
      nextDate = nextDate.add(const Duration(days: 1));
    }
    return nextDate;
  }

  // Find the next occurrence of a specific weekday
  DateTime _getNextSpecificWeekday(int targetWeekday) {
    return getNextWeekdayDate(DateTime.now(), targetWeekday);
  }

  // Get the next date for a specific weekday
  DateTime getNextWeekdayDate(DateTime today, int weekday) {
    // Ensure weekday is 1-5 (Monday-Friday)
    if (weekday < 1 || weekday > 5) {
      throw ArgumentError(
          'Invalid weekday: $weekday. Must be 1-5 (Monday-Friday)');
    }

    // Calculate offset to next occurrence of the weekday
    int offset = (weekday - today.weekday + 7) % 7;

    // If the calculated day is today (offset == 0), return next week instead
    return today.add(Duration(days: offset == 0 ? 7 : offset));
  }

  // Get indexes of selected weekdays
  List<int> _getSelectedWeekdayIndexes() {
    List<int> selectedIndexes = [];
    for (int i = 0; i < _selectedWeekdays.length; i++) {
      if (_selectedWeekdays[i]) {
        // Add 1 to match DateTime.weekday (1-7) where 1 is Monday
        selectedIndexes.add(i + 1);
      }
    }
    return selectedIndexes;
  }

  // Map delivery day to weekday index (0-6 for Mon-Sun)
  int _getWeekdayIndexFromDeliveryDay(Map<String, String> deliveryDay) {
    final shortName = deliveryDay['short_name']?.toLowerCase() ?? '';
    switch (shortName) {
      case 'mon':
        return 0;
      case 'tue':
        return 1;
      case 'wed':
        return 2;
      case 'thu':
        return 3;
      case 'fri':
        return 4;
      case 'sat':
        return 5;
      case 'sun':
        return 6;
      default:
        return 0; // Default to Monday if unknown
    }
  }

  // Get user-friendly error message from exception
  String _getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network connection issue';
    } else if (errorString.contains('timeout')) {
      return 'Request timed out';
    } else if (errorString.contains('server') || errorString.contains('500')) {
      return 'Server temporarily unavailable';
    } else if (errorString.contains('404')) {
      return 'Service not found';
    } else if (errorString.contains('invalid') ||
        errorString.contains('format')) {
      return 'Invalid data format';
    } else {
      return 'Service temporarily unavailable';
    }
  }

  // Find the earliest date based on selected weekdays
  DateTime _findEarliestDateFromSelectedWeekdays() {
    if (_selectedWeekdays.where((day) => day).isEmpty) {
      // If no weekdays selected, return the default next weekday
      return _getNextWeekday(DateTime.now());
    }

    // Get the list of selected weekday indices (1-based, Monday=1)
    final List<int> selectedWeekdayIndices = _getSelectedWeekdayIndexes();

    // Get all upcoming dates for selected weekdays
    final List<DateTime> upcomingDates = selectedWeekdayIndices
        .map((weekday) => getNextWeekdayDate(DateTime.now(), weekday))
        .toList();

    // Sort dates to find the earliest
    upcomingDates.sort();

    return upcomingDates.first;
  }

  // Calculate custom plan start date based on selected date and weekdays
  DateTime calculateCustomPlanStartDate({
    required DateTime selectedStartDate,
    required List<int> selectedWeekdays,
  }) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day); // Remove time

    final selectedStart = DateTime(
      selectedStartDate.year,
      selectedStartDate.month,
      selectedStartDate.day,
    );

    final difference = selectedStart.difference(today).inDays;
    final isInCurrentWeek = difference >= 0 && difference < 7;

    if (isInCurrentWeek) {
      final upcomingDates = selectedWeekdays
          .map((weekday) => getNextWeekdayDate(today, weekday))
          .where((date) => !date.isBefore(today))
          .toList()
        ..sort();

      return upcomingDates.isNotEmpty ? upcomingDates.first : selectedStart;
    }

    return selectedStart; // Respect manually selected date
  }

  // Modified _calculateMealDates to handle chip visibility
  void _calculateMealDates() {
    if (widget.isExpressOrder) {
      _calculateExpressMealDates();
      _showMealChip = _shouldShowMealChip();
      return;
    }

    // For regular plans
    _mealDates.clear();

    if (_selectedPlanIndex == 0) {
      // Single Day - Add just the start date
      // Make sure we have a proper start date for single day plans
      DateTime now = DateTime.now();
      DateTime todayDate = DateTime(now.year, now.month, now.day);

      // If start date hasn't been manually selected yet, set it to next available weekday
      if (!_isStartDateSelected()) {
        _startDate = _getNextWeekday(todayDate);
        _focusedCalendarDate = _startDate;
      }

      _mealDates.add(_startDate);
      _endDate = _startDate;
    } else {
      // Multi-day subscriptions
      if (_isCustomPlan) {
        // Custom plan with selected weekdays
        _calculateCustomPlanMealDates();
      } else {
        // Regular plan (Monday to Friday)
        _calculateRegularPlanMealDates();
      }
    }

    _showMealChip = _shouldShowMealChip();
  }

  // Calculate express meal dates for express 1-day orders
  void _calculateExpressMealDates() {
    _mealDates.clear();

    // For express orders, always use a fixed date - either today (if in express window)
    // or the next available weekday
    final now = DateTime.now();
    final bool isExpressWindowOpen = isWithinExpressWindow();

    // If express window is open and it's a weekday, use today
    if (isExpressWindowOpen && now.weekday <= 5) {
      _startDate = DateTime(now.year, now.month, now.day);
    } else {
      // Otherwise use the next weekday
      _startDate = _getNextWeekday(now);
    }

    // Always use just one date - the fixed express date
    _mealDates.add(_startDate);
    _endDate = _startDate;

    // Debug print
    print('Express meal date: $_startDate');
  }

  // Calculate custom plan meal dates
  void _calculateCustomPlanMealDates() {
    _mealDates = [];

    final selectedWeekdayIndexes = _getSelectedWeekdayIndexes();
    final totalMeals = _getPlanData(_selectedPlanIndex)['meals'];

    // For custom plan, update the start date to the earliest selected weekday
    _startDate = calculateCustomPlanStartDate(
      selectedStartDate: _startDate,
      selectedWeekdays: selectedWeekdayIndexes,
    );
    _focusedCalendarDate = _startDate;

    // Generate meal dates
    _mealDates = _generateMealDates(
      startFrom: _startDate,
      selectedWeekdays: selectedWeekdayIndexes,
      totalMeals: totalMeals,
      isSingleDay: false,
    );

    // Set end date to the last meal date
    if (_mealDates.isNotEmpty) {
      setState(() {
        _endDate = _mealDates.last;
      });
    } else {
      setState(() {
        _endDate = null;
      });
    }

    // Debug log
    print(
        'Selected weekdays: ${selectedWeekdayIndexes.map((i) => _weekdayNames[i - 1]).toList()}');
    print('Start: $_startDate, End: $_endDate');
    print('Meal dates count: ${_mealDates.length}');
  }

  // Calculate regular plan meal dates
  void _calculateRegularPlanMealDates() {
    _mealDates = [];

    final selectedWeekdayIndexes = _getSelectedWeekdayIndexes();
    final totalMeals = _getPlanData(_selectedPlanIndex)['meals'];

    // Generate meal dates
    _mealDates = _generateMealDates(
      startFrom: _startDate,
      selectedWeekdays: selectedWeekdayIndexes,
      totalMeals: totalMeals,
      isSingleDay: false,
    );

    // Set end date to the last meal date
    if (_mealDates.isNotEmpty) {
      setState(() {
        _endDate = _mealDates.last;
      });
    } else {
      setState(() {
        _endDate = null;
      });
    }

    // Debug log
    print(
        'Selected weekdays: ${selectedWeekdayIndexes.map((i) => _weekdayNames[i - 1]).toList()}');
    print('Start: $_startDate, End: $_endDate');
    print('Meal dates count: ${_mealDates.length}');
  }

  // Generate a list of meal dates based on selected weekdays and plan
  List<DateTime> _generateMealDates({
    required DateTime startFrom,
    required List<int> selectedWeekdays,
    required int totalMeals,
    bool isSingleDay = false,
  }) {
    if (selectedWeekdays.isEmpty) return [];

    // For single day plan, just return the start date
    if (isSingleDay) {
      return [startFrom];
    }

    List<DateTime> mealDates = [];
    DateTime current = startFrom;

    // Continue until we've collected all meal dates or reached a reasonable limit
    int safetyCounter = 0;
    while (mealDates.length < totalMeals && safetyCounter < 1000) {
      // Check if current date is a selected weekday (and not a weekend)
      if (selectedWeekdays.contains(current.weekday) && current.weekday <= 5) {
        mealDates.add(current);
      }

      // Move to next day
      current = current.add(const Duration(days: 1));
      safetyCounter++;
    }

    return mealDates;
  }

  // Check if a date has a meal scheduled
  bool _hasMealOnDate(DateTime date) {
    return _mealDates.any((mealDate) =>
        mealDate.year == date.year &&
        mealDate.month == date.month &&
        mealDate.day == date.day);
  }

  // Calculate end date based on selected plan and weekdays
  void _calculateEndDate() {
    _calculateMealDates();
  }

  // Get all meal delivery dates
  List<DateTime> _getMealDeliveryDates() {
    return _mealDates;
  }

  // Modified _handleWeekdaySelection to handle chip visibility
  void _handleWeekdaySelection(int index, bool? value) {
    // Ensure at least one weekday is selected
    final wouldHaveSelection =
        _selectedWeekdays.asMap().entries.any((e) => e.key != index && e.value);

    if (value == false && !wouldHaveSelection) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'You must select at least one weekday',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _selectedWeekdays[index] = value!;
      _showMealChip = false;

      // Update the start date based on the earliest selected weekday
      if (_isCustomPlan) {
        _startDate = calculateCustomPlanStartDate(
          selectedStartDate: _startDate,
          selectedWeekdays: _getSelectedWeekdayIndexes(),
        );
        _focusedCalendarDate = _startDate;

        // Also update the end date based on the selected plan
        final int weeks = _getPlanData(_selectedPlanIndex)['weeks'] as int;
        _calculatePlanEndDate(weeks);
      }

      // Recalculate meal dates with the new start date
      _calculateMealDates();
      _showMealChip = _shouldShowMealChip();
    });

    // Reload working days when custom days change (debounced for better UX)
    if (_isCustomPlan) {
      _loadWorkingDaysDebounced();
    }
  }

  // Handle plan selection change
  void _handlePlanSelection(int index) {
    final bool isSingleDay = _getPlanData(index)['isSingleDay'] ?? false;

    setState(() {
      _selectedPlanIndex = index;
      _showMealChip = false;

      // Reset to regular plan mode if Single Day is selected
      if (isSingleDay) {
        _isCustomPlan = false;
      }

      _calculateMealDates();
      _showMealChip = _shouldShowMealChip();
    });
  }

  // Get formatted string of selected weekdays
  String _getSelectedWeekdaysText() {
    List<String> selectedDays = [];
    for (int i = 0; i < _selectedWeekdays.length; i++) {
      if (_selectedWeekdays[i]) {
        selectedDays.add(_weekdayNames[i]);
      }
    }

    if (selectedDays.isEmpty) {
      return "None";
    } else if (selectedDays.length == 5) {
      return "All Weekdays";
    } else if (selectedDays.length >= 3) {
      // For 3 or more days, use abbreviated form to save space
      return selectedDays.map((day) => day.substring(0, 3)).join(", ");
    } else {
      return selectedDays.join(", ");
    }
  }

  // Calculate price with or without discount
  double _calculatePrice() {
    final baseCost = widget.totalMealCost;
    final isSingleDay =
        _getPlanData(_selectedPlanIndex)['isSingleDay'] ?? false;

    // For single day plan, always use 1 meal
    final mealCount = isSingleDay
        ? 1
        : (_isCustomPlan
            ? _mealDates.length
            : _getPlanData(_selectedPlanIndex)['meals']);

    final discount = _getPlanData(_selectedPlanIndex)['discount'];

    final totalPrice = baseCost * mealCount;
    final discountedPrice = totalPrice * (1 - discount);

    // Store combined price for breakfast and lunch
    if (widget.mealType == 'breakfast_and_lunch') {
      _combinedPrice = discountedPrice;
    }

    return discountedPrice;
  }

  // Calculate original price before discount
  double _calculateOriginalPrice() {
    // Get currently selected meals from UI
    List<Meal> currentSelectedMeals = _getCurrentlySelectedMeals();

    if (currentSelectedMeals.isEmpty) {
      return 0.0;
    }

    // Calculate base cost per day
    double baseCostPerDay =
        currentSelectedMeals.fold(0.0, (sum, meal) => sum + meal.price);

    // Get meal count based on plan
    final isSingleDay =
        _getPlanData(_selectedPlanIndex)['isSingleDay'] ?? false;
    final mealCount = isSingleDay
        ? 1
        : (_isCustomPlan
            ? _mealDates.length
            : _getPlanData(_selectedPlanIndex)['meals']);

    // Calculate total price without discount
    return baseCostPerDay * mealCount;
  }

  // Calculate final price with discount
  double _getCombinedPrice() {
    final originalPrice = _calculateOriginalPrice();
    if (originalPrice == 0.0) return 0.0;

    // Get discount percentage for the selected plan
    final discount = _getPlanData(_selectedPlanIndex)['discount'];

    // Calculate discounted price
    final discountedPrice = originalPrice * (1 - discount);

    // Store individual prices for breakfast and lunch if needed
    List<Meal> currentSelectedMeals = _getCurrentlySelectedMeals();
    List<Meal> breakfastMeals = currentSelectedMeals
        .where((meal) => meal.categories.contains(MealCategory.breakfast))
        .toList();
    List<Meal> lunchMeals = currentSelectedMeals
        .where((meal) => meal.categories.contains(MealCategory.lunch))
        .toList();

    if (breakfastMeals.isNotEmpty && lunchMeals.isNotEmpty) {
      // Calculate proportion of breakfast and lunch in total
      double breakfastProportion =
          breakfastMeals.fold(0.0, (sum, meal) => sum + meal.price) /
              currentSelectedMeals.fold(0.0, (sum, meal) => sum + meal.price);

      _breakfastPrice = discountedPrice * breakfastProportion;
      _lunchPrice = discountedPrice * (1 - breakfastProportion);
    } else if (breakfastMeals.isNotEmpty) {
      _breakfastPrice = discountedPrice;
      _lunchPrice = 0.0;
    } else if (lunchMeals.isNotEmpty) {
      _breakfastPrice = 0.0;
      _lunchPrice = discountedPrice;
    }

    return discountedPrice;
  }

  // Get savings amount
  double _getSavings() {
    final originalPrice = _calculateOriginalPrice();
    final discountedPrice = _getCombinedPrice();
    return originalPrice - discountedPrice;
  }

  // Get formatted start date string including weekday name
  String _getFormattedStartDate() {
    if (_selectedWeekdays.where((day) => day).isEmpty) {
      return "No weekdays selected";
    }

    final String weekdayName = DateFormat('EEEE').format(_startDate);
    final String formattedDate = DateFormat('MMMM d, yyyy').format(_startDate);
    return "$weekdayName, $formattedDate";
  }

  // Ensure the focused day is valid and within the range of firstDay and lastDay
  DateTime _ensureValidFocusedDay() {
    // If focusedDay is null, use startDate
    if (_focusedCalendarDate == null) {
      log('📅 _focusedCalendarDate is null, using _startDate: ${_startDate.month}/${_startDate.year}');
      return _startDate;
    }

    // Ensure focusedDay is not before firstDay
    if (_focusedCalendarDate!.isBefore(_firstAvailableDate)) {
      log('📅 _focusedCalendarDate is before firstAvailableDate, using firstAvailableDate: ${_firstAvailableDate.month}/${_firstAvailableDate.year}');
      return _firstAvailableDate;
    }

    // Ensure focusedDay is not after lastDay (using the new comprehensive range)
    if (_focusedCalendarDate!.isAfter(_lastAvailableDate)) {
      log('📅 _focusedCalendarDate is after lastAvailableDate, using lastAvailableDate: ${_lastAvailableDate.month}/${_lastAvailableDate.year}');
      return _lastAvailableDate;
    }

    log('📅 Using valid _focusedCalendarDate: ${_focusedCalendarDate!.month}/${_focusedCalendarDate!.year}');
    return _focusedCalendarDate!;
  }

  // Update plan index and also check if we need to force it to Custom or Regular mode
  void _selectPlan(int index) {
    final bool isSingleDay = _getPlanData(index)['isSingleDay'] ?? false;
    final int weeks = _getPlanData(index)['weeks'] as int;

    setState(() {
      _selectedPlanIndex = index;
      // If Single Day or Express plan, force to Regular mode
      if (index == 0 || widget.isExpressOrder) {
        _isCustomPlan = false;

        // For Single Day plan, always ensure start date is set
        if (isSingleDay) {
          DateTime now = DateTime.now();
          DateTime todayDate = DateTime(now.year, now.month, now.day);

          // Always ensure we have a good date for single day plans
          if (!_isStartDateSelected()) {
            _startDate = _getNextWeekday(todayDate);
            _focusedCalendarDate = _startDate;
          }
          _endDate =
              _startDate; // Single day plans have same start and end date
        } else {
          // For regular multi-day plans, ensure the start date is set to next available weekday
          if (!_isStartDateSelected()) {
            DateTime now = DateTime.now();
            DateTime todayDate = DateTime(now.year, now.month, now.day);
            _startDate = _getNextWeekday(todayDate);
            _focusedCalendarDate = _startDate;
          }

          // Calculate and set the end date based on the plan's duration
          _calculatePlanEndDate(weeks);
        }
      }
      // Calculate meal dates and end dates
      _calculateMealDates();
    });

    // Calculate end date with API when plan changes
    _calculateEndDateWithAPI();
  }

  // Helper to calculate end date based on plan duration in weeks
  void _calculatePlanEndDate(int weeks) {
    if (weeks <= 0) {
      // Single day plan
      _endDate = _startDate;
      return;
    }

    // Get the delivery weekdays (regular or custom)
    List<int> deliveryWeekdays = _isCustomPlan
        ? _getSelectedWeekdayIndexes()
        : [1, 2, 3, 4, 5]; // Mon-Fri for regular

    if (deliveryWeekdays.isEmpty) {
      // If no weekdays selected in custom mode, use all weekdays
      deliveryWeekdays = [1, 2, 3, 4, 5];
    }

    // Calculate number of days needed to get the required number of delivery days
    int daysToAdd = 0;
    int deliveryDaysFound = 0;
    int mealsRequired = _getPlanData(_selectedPlanIndex)['meals'] as int;
    int safetyCounter = 0;

    DateTime current = _startDate;

    // Calculate end date by finding the day when we reach the total number of meals
    while (deliveryDaysFound < mealsRequired && safetyCounter < 1000) {
      daysToAdd++;
      current = _startDate.add(Duration(days: daysToAdd));

      if (deliveryWeekdays.contains(current.weekday)) {
        deliveryDaysFound++;
      }

      safetyCounter++;
    }

    _endDate = current;
  }

  // Toggle custom plan mode
  void _toggleCustomMode() {
    if (_selectedPlanIndex != 0 && !widget.isExpressOrder) {
      final int weeks = _getPlanData(_selectedPlanIndex)['weeks'] as int;

      setState(() {
        _isCustomPlan = !_isCustomPlan;

        if (!_isCustomPlan) {
          // Switching to regular mode (Mon to Fri)
          _selectedWeekdays =
              EndDateCalculationService.getDefaultWeekdaySelection();

          // Reset to next available weekday if no date selected yet
          if (!_isStartDateSelected()) {
            DateTime now = DateTime.now();
            DateTime todayDate = DateTime(now.year, now.month, now.day);
            _startDate = _getNextWeekday(todayDate);
          }
        } else {
          // Switching to custom mode
          // Start with only Monday selected
          _selectedWeekdays = List.filled(7, false);
          _selectedWeekdays[0] = true; // Select Monday

          // Reset to next available Monday if no date selected yet
          if (!_isStartDateSelected()) {
            DateTime now = DateTime.now();
            DateTime todayDate = DateTime(now.year, now.month, now.day);
            _startDate = getNextWeekdayDate(todayDate, 1); // 1 is Monday
          }
        }

        _focusedCalendarDate = _startDate;

        // Calculate the end date based on current plan
        _calculatePlanEndDate(weeks);

        // Calculate meal dates
        _calculateMealDates();
      });

      // Reload working days when custom mode is toggled
      _loadWorkingDays();

      // Calculate end date with API when custom mode is toggled
      _calculateEndDateWithAPI();
    }
  }

  // Toggle weekday selection for custom plan
  void _toggleWeekday(int index) {
    if (_selectedPlanIndex != 0 && !widget.isExpressOrder) {
      setState(() {
        // Toggle the selection for this weekday
        _selectedWeekdays[index] = !_selectedWeekdays[index];

        // Ensure at least one weekday is selected
        bool anySelected = _selectedWeekdays.any((day) => day);
        if (!anySelected) {
          // If none selected, revert the change
          _selectedWeekdays[index] = true;
          return;
        }

        // Update start date based on selected weekdays if in custom mode
        if (_isCustomPlan) {
          _startDate = _findEarliestDateFromSelectedWeekdays();
        }

        // Calculate end date and meal dates
        _calculateMealDates();
      });

      // Calculate end date with API when weekdays change
      _calculateEndDateWithAPI();
    }
  }

  // Handle date selection from calendar
  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      // Only update if the selected date is not before the min date
      if (!selectedDay.isBefore(_firstAvailableDate)) {
        _startDate = selectedDay;
        _focusedCalendarDate = focusedDay;
        _calculateMealDates();
        _showMealChip = _shouldShowMealChip();
      }
    });

    // Calculate end date with API when start date changes
    _calculateEndDateWithAPI();
  }

  void _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: isPreOrder && currentEndDate != null
          ? currentEndDate!.add(
              const Duration(days: 1)) // Start from day after current plan ends
          : DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        selectedStartDate = picked;
        // Set end date to 30 days after start date
        selectedEndDate = picked.add(const Duration(days: 30));
      });
    }
  }

  void _proceedToCheckout() {
    if (selectedPlan == null ||
        selectedStartDate == null ||
        selectedEndDate == null) {
      ToastUtils.showToast(
        context: context,
        message: 'Please select a plan and dates',
        type: ToastType.warning,
      );
      return;
    }

    // Validate pre-order start date
    if (isPreOrder && currentEndDate != null) {
      if (selectedStartDate!.isBefore(currentEndDate!)) {
        ToastUtils.showToast(
          context: context,
          message: 'Pre-order start date must be after current plan end date',
          type: ToastType.warning,
        );
        return;
      }
    }

    Navigator.pushNamed(
      context,
      '/checkout',
      arguments: {
        'plan': selectedPlan,
        'startDate': selectedStartDate,
        'endDate': selectedEndDate,
        'isPreOrder': isPreOrder,
      },
    );
  }

  // Add meal selection methods from MealPlanScreen
  void _handleMealSelection(Meal meal) {
    setState(() {
      if (meal.id == 'breakfast') {
        // Handle breakfast selection
        bool wasSelected = false;
        for (var m in _breakfastMeals) {
          if (m['name'] == meal.name && m['isSelected']) {
            // If already selected, unselect it
            m['isSelected'] = false;
            wasSelected = true;
          } else {
            m['isSelected'] = m['name'] == meal.name;
          }
        }
        // Close breakfast section and open lunch section if a meal was selected
        _isBreakfastExpanded = false;
        // Only open lunch section if lunch is not selected yet
        if (!wasSelected && meal.name != '' && !_isLunchSelected()) {
          _isLunchExpanded = true;
        }
      } else {
        // Handle lunch selection
        bool wasSelected = false;
        for (var m in _lunchMeals) {
          if (m['name'] == meal.name && m['isSelected']) {
            // If already selected, unselect it
            m['isSelected'] = false;
            wasSelected = true;
          } else {
            m['isSelected'] = m['name'] == meal.name;
          }
        }
        // Close lunch section and open breakfast section if a meal was selected
        _isLunchExpanded = false;
        // Only open breakfast section if breakfast is not selected yet
        if (!wasSelected && meal.name != '' && !_isBreakfastSelected()) {
          _isBreakfastExpanded = true;
        }
      }

      // Update current selected meals and costs
      _updateCurrentSelectedMeals();
    });
  }

  // Helper method to check if breakfast is selected
  bool _isBreakfastSelected() {
    return _breakfastMeals.any((meal) => meal['isSelected']);
  }

  // Helper method to check if lunch is selected
  bool _isLunchSelected() {
    return _lunchMeals.any((meal) => meal['isSelected']);
  }

  // Update current selected meals for navigation
  void _updateCurrentSelectedMeals() {
    List<Meal> selectedMeals = [];
    double totalMealCost = 0;
    String mealType = '';

    // Check breakfast selection
    final selectedBreakfast = _breakfastMeals.firstWhere(
      (meal) => meal['isSelected'],
      orElse: () => {'name': '', 'price': 0, 'isSelected': false},
    );

    // Check lunch selection
    final selectedLunch = _lunchMeals.firstWhere(
      (meal) => meal['isSelected'],
      orElse: () => {'name': '', 'price': 0, 'isSelected': false},
    );

    // Add selected breakfast if any
    if (selectedBreakfast['isSelected']) {
      selectedMeals.add(Meal(
        id: 'breakfast',
        name: selectedBreakfast['name'],
        description: _getMealDescription(selectedBreakfast['name']),
        price: selectedBreakfast['price'] as double,
        type: MealType.veg,
        categories: [MealCategory.breakfast],
        imageUrl: '',
        ingredients: [],
        nutritionalInfo: {},
        allergyInfo: [],
      ));
      totalMealCost += selectedBreakfast['price'];
      mealType = 'breakfast';
    }

    // Add selected lunch if any
    if (selectedLunch['isSelected']) {
      selectedMeals.add(Meal(
        id: 'lunch',
        name: selectedLunch['name'],
        description: _getMealDescription(selectedLunch['name']),
        price: selectedLunch['price'] as double,
        type: MealType.veg,
        categories: [MealCategory.lunch],
        imageUrl: '',
        ingredients: [],
        nutritionalInfo: {},
        allergyInfo: [],
      ));
      totalMealCost += selectedLunch['price'];
      mealType = selectedMeals.length > 1 ? 'breakfast_and_lunch' : 'lunch';
    }

    _currentSelectedMeals = selectedMeals;
    _currentTotalMealCost = totalMealCost;
    _currentMealType = mealType;
  }

  Widget _buildSectionHeader({
    required String title,
    required IconData icon,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingSection(String mealType) {
    return Container(
      margin: const EdgeInsets.only(bottom: 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.purple),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Loading $mealType Options...',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Please wait while we fetch the latest menu',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorSection(String mealType, String error) {
    return Container(
      margin: const EdgeInsets.only(bottom: 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade400,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Failed to Load $mealType',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.red.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Please check your connection and try again',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: _loadProductMenu,
            child: Text(
              'Retry',
              style: GoogleFonts.poppins(
                color: AppTheme.purple,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDisabledSection(String mealType) {
    return Container(
      margin: const EdgeInsets.only(bottom: 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(
            mealType == 'Breakfast'
                ? MealConstants.breakfastIcon
                : MealConstants.lunchIcon,
            color: Colors.grey.shade400,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Already in Cart',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'You already have a $mealType meal in your cart',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandableMealSection({
    required String mealType,
    required List<Map<String, dynamic>> meals,
    required bool isDisabled,
    required Color iconColor,
    required IconData icon,
    required bool isExpanded,
    required VoidCallback onToggle,
  }) {
    // Find selected meal if any
    final selectedMeal = meals.firstWhere(
      (meal) => meal['isSelected'],
      orElse: () => {'name': '', 'price': 0, 'isSelected': false},
    );

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDisabled
              ? Colors.grey.shade300
              : selectedMeal['isSelected']
                  ? iconColor
                  : iconColor.withOpacity(0.3),
          width: selectedMeal['isSelected'] ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: iconColor.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: isDisabled ? null : onToggle,
            borderRadius: isExpanded
                ? const BorderRadius.vertical(top: Radius.circular(11))
                : BorderRadius.circular(11),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: selectedMeal['isSelected']
                    ? iconColor.withOpacity(0.05)
                    : Colors.white,
                borderRadius: isExpanded
                    ? const BorderRadius.vertical(top: Radius.circular(11))
                    : BorderRadius.circular(11),
              ),
              child: Row(
                children: [
                  Icon(
                    icon,
                    color: isDisabled ? Colors.grey : iconColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          selectedMeal['isSelected']
                              ? selectedMeal['name']
                              : 'Select ${mealType.toLowerCase()}',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isDisabled ? Colors.grey : AppTheme.textDark,
                          ),
                        ),
                        if (selectedMeal['isSelected']) ...[
                          const SizedBox(height: 4),
                          Text(
                            _getMealDescription(selectedMeal['name']),
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: AppTheme.textMedium,
                            ),
                          ),
                        ] else ...[
                          const SizedBox(height: 4),
                          Text(
                            'No meal selected',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (selectedMeal['isSelected']) ...[
                    IconButton(
                      onPressed: () => _handleMealSelection(
                        Meal(
                          id: mealType.toLowerCase(),
                          name: selectedMeal['name'],
                          description:
                              _getMealDescription(selectedMeal['name']),
                          price: (selectedMeal['price'] as double).toDouble(),
                          type: MealType.veg,
                          categories: [
                            mealType.toLowerCase() == 'breakfast'
                                ? MealCategory.breakfast
                                : MealCategory.lunch
                          ],
                          imageUrl: '',
                          ingredients: [],
                          nutritionalInfo: {},
                          allergyInfo: [],
                        ),
                      ),
                      icon: Icon(
                        Icons.close,
                        color: Colors.red,
                        size: 20,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isDisabled ? Colors.grey : iconColor,
                    size: 28,
                  ),
                ],
              ),
            ),
          ),
          // Expanded meal options
          AnimatedCrossFade(
            firstChild: const SizedBox(height: 0),
            secondChild: Column(
              children: [
                Divider(
                  height: 1,
                  color: Colors.grey.withOpacity(0.2),
                ),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.zero,
                  itemCount: meals.length,
                  itemBuilder: (context, index) {
                    final meal = meals[index];
                    return InkWell(
                      onTap: () {
                        _handleMealSelection(
                          Meal(
                            id: mealType.toLowerCase(),
                            name: meal['name'],
                            description: _getMealDescription(meal['name']),
                            price: meal['price'] as double,
                            type: MealType.veg,
                            categories: [
                              mealType.toLowerCase() == 'breakfast'
                                  ? MealCategory.breakfast
                                  : MealCategory.lunch
                            ],
                            imageUrl: '',
                            ingredients: [],
                            nutritionalInfo: {},
                            allergyInfo: [],
                          ),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: meal['isSelected']
                              ? iconColor.withOpacity(0.05)
                              : Colors.white,
                          border: Border(
                            bottom: index != meals.length - 1
                                ? BorderSide(
                                    color: Colors.grey.withOpacity(0.2),
                                    width: 1,
                                  )
                                : BorderSide.none,
                          ),
                        ),
                        child: Row(
                          children: [
                            const VegIcon(),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    meal['name'],
                                    style: GoogleFonts.poppins(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: AppTheme.textDark,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _getMealDescription(meal['name']),
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                      color: AppTheme.textMedium,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: meal['isSelected']
                                    ? iconColor.withOpacity(0.1)
                                    : Colors.transparent,
                                shape: BoxShape.circle,
                              ),
                              child: meal['isSelected']
                                  ? Icon(
                                      Icons.check,
                                      color: iconColor,
                                      size: 22,
                                    )
                                  : Icon(
                                      Icons.circle_outlined,
                                      color: Colors.grey.shade400,
                                      size: 22,
                                    ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
            crossFadeState: isExpanded
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }

  String _getMealDescription(String mealName) {
    final name = mealName.toLowerCase().trim();

    // Breakfast descriptions
    if (name == 'breakfast of the day') {
      return 'A rotating menu of Indian and International options';
    }
    if (name == 'indian breakfast') {
      return 'Traditional Indian morning meals';
    }
    if (name == 'international breakfast') {
      return 'Global breakfast favorites';
    }
    if (name == 'jain breakfast') {
      return 'Pure vegetarian without root vegetables';
    }

    // Lunch descriptions
    if (name == 'lunch of the day') {
      return 'Chef\'s special rotating menu';
    }
    if (name == 'indian lunch') {
      return 'Traditional Indian cuisine';
    }
    if (name == 'international lunch') {
      return 'Global flavors and dishes';
    }
    if (name == 'jain lunch') {
      return 'Pure vegetarian without root vegetables';
    }

    return 'Delicious and nutritious meal option';
  }

  @override
  Widget build(BuildContext context) {
    // Monitor focused day changes for arrow button navigation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _monitorFocusedDayChanges();
    });

    final hasDiscount = _getPlanData(_selectedPlanIndex)['discount'] > 0;

    // Find the highest discount for Best Value badge
    final double maxDiscount = _planDurations.isNotEmpty
        ? _planDurations
            .map((d) => d.discountPercentage ?? 0.0)
            .reduce((a, b) => a > b ? a : b)
        : 0.0;

    return Scaffold(
      appBar: GlobalAppBar(
        title: 'Choose Your Plan',
        showBackButton: true,
        onBackPressed: () {
          // Navigate back to the main screen since Order Meal page no longer exists
          Navigator.pushNamedAndRemoveUntil(
              context, Routes.main, (route) => false);
        },
        hideProfileIcon: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Express Order Banner (only for Express orders)
                    if (widget.isExpressOrder)
                      InfoBanner(
                        title: MealPlanValidator.isWithinExpressWindow()
                            ? "Express 1-Day Delivery"
                            : "Express Order Window Closed",
                        message: MealPlanValidator.isWithinExpressWindow()
                            ? "Orders for same-day delivery are available between 12:00 AM to 8:00 AM (IST). Please confirm your order below."
                            : "Express orders are only available between 12:00 AM and 8:00 AM IST. Please try again during this time window.",
                        type: MealPlanValidator.isWithinExpressWindow()
                            ? InfoBannerType.success
                            : InfoBannerType.warning,
                      ),

                    // Show some space if Express banner is shown
                    if (widget.isExpressOrder) const SizedBox(height: 8),

                    // Add Breakfast & Lunch Selection UI here (above Plan selection)
                    // Add "Select Meals" label
                    Text(
                      "Select Meals",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textDark,
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Breakfast Section (no header label)
                    if (_isLoadingMeals)
                      _buildLoadingSection('Breakfast')
                    else if (_mealLoadError != null)
                      _buildErrorSection('Breakfast', _mealLoadError!)
                    else if (isBreakfastDisabled)
                      _buildDisabledSection('Breakfast')
                    else
                      _buildExpandableMealSection(
                        mealType: 'Breakfast',
                        meals: _breakfastMeals,
                        isDisabled: isBreakfastDisabled,
                        iconColor: MealConstants.breakfastIconColor,
                        icon: MealConstants.breakfastIcon,
                        isExpanded: _isBreakfastExpanded,
                        onToggle: () => setState(
                            () => _isBreakfastExpanded = !_isBreakfastExpanded),
                      ),

                    const SizedBox(height: 12),

                    // Lunch Section (no header label)
                    if (_isLoadingMeals)
                      _buildLoadingSection('Lunch')
                    else if (_mealLoadError != null)
                      _buildErrorSection('Lunch', _mealLoadError!)
                    else if (isLunchDisabled)
                      _buildDisabledSection('Lunch')
                    else
                      _buildExpandableMealSection(
                        mealType: 'Lunch',
                        meals: _lunchMeals,
                        isDisabled: isLunchDisabled,
                        iconColor: MealConstants.lunchIconColor,
                        icon: MealConstants.lunchIcon,
                        isExpanded: _isLunchExpanded,
                        onToggle: () => setState(
                            () => _isLunchExpanded = !_isLunchExpanded),
                      ),

                    const SizedBox(height: 20),

                    // Plan selection heading
                    Text(
                      widget.isExpressOrder
                          ? "Express 1-Day Delivery Plan"
                          : "Duration",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textDark,
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Plan selection cards - improved grid layout
                    if (_isLoadingDurations)
                      _buildPlanDurationLoadingSection()
                    else if (_durationLoadError != null)
                      _buildPlanDurationErrorSection(_durationLoadError!)
                    else if (widget.isExpressOrder)
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: _buildExpressOnlyPlanCard(),
                      )
                    else
                      LayoutBuilder(
                        builder: (context, constraints) {
                          final double screenWidth = constraints.maxWidth;
                          final double maxDiscount = _planDurations.isNotEmpty
                              ? _planDurations
                                  .map((d) => d.discountPercentage ?? 0.0)
                                  .reduce((a, b) => a > b ? a : b)
                              : _subscriptionPlans
                                  .map((p) => p['discount'] as double)
                                  .reduce((a, b) => a > b ? a : b);

                          // Responsive grid configuration
                          int crossAxisCount;
                          double childAspectRatio;
                          double horizontalPadding;
                          double cardPadding;

                          if (screenWidth < 400) {
                            // Small phones - 2 columns
                            crossAxisCount = 2;
                            childAspectRatio = 0.85;
                            horizontalPadding = 8.0;
                            cardPadding = 12.0;
                          } else if (screenWidth < 600) {
                            // Medium phones - 3 columns
                            crossAxisCount = 3;
                            childAspectRatio = 0.9;
                            horizontalPadding = 12.0;
                            cardPadding = 14.0;
                          } else {
                            // Large screens - 3 columns with more space
                            crossAxisCount = 3;
                            childAspectRatio = 1.0;
                            horizontalPadding = 16.0;
                            cardPadding = 16.0;
                          }

                          return Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: horizontalPadding),
                            child: GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: crossAxisCount,
                                childAspectRatio: childAspectRatio,
                                mainAxisSpacing: 12.0,
                                crossAxisSpacing: 12.0,
                              ),
                              itemCount: _planDurations.isNotEmpty
                                  ? _planDurations.length
                                  : _subscriptionPlans.length,
                              itemBuilder: (context, index) {
                                final plan = _getPlanData(index);
                                final isSelected = _selectedPlanIndex == index;
                                final isBestValue =
                                    plan['discount'] == maxDiscount &&
                                        maxDiscount > 0;
                                final perMealPrice = widget.totalMealCost > 0 &&
                                        plan['meals'] > 0
                                    ? (widget.totalMealCost *
                                            plan['meals'] *
                                            (1 - plan['discount'])) /
                                        plan['meals']
                                    : 0.0;

                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _selectedPlanIndex = index;
                                    });
                                  },
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 200),
                                    curve: Curves.easeInOut,
                                    padding: EdgeInsets.all(cardPadding),
                                    margin: const EdgeInsets.all(
                                        4), // Add margin for badges
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                      gradient: isSelected
                                          ? LinearGradient(
                                              colors: [
                                                AppTheme.purple,
                                                AppTheme.deepPurple
                                              ],
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                            )
                                          : null,
                                      color: isSelected ? null : Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: isSelected
                                              ? AppTheme.purple
                                                  .withOpacity(0.25)
                                              : Colors.grey.withOpacity(0.15),
                                          blurRadius: isSelected ? 15 : 6,
                                          offset: Offset(0, isSelected ? 6 : 3),
                                        ),
                                      ],
                                      border: Border.all(
                                        color: isSelected
                                            ? AppTheme.purple
                                            : Colors.grey.shade200,
                                        width: isSelected ? 2.5 : 1.5,
                                      ),
                                    ),
                                    child: Stack(
                                      children: [
                                        // Discount badge - enhanced (top right, moved to avoid overlap)
                                        if (plan['discount'] != null &&
                                            plan['discount'] > 0)
                                          Positioned(
                                            top: -2,
                                            right: -2,
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  colors: [
                                                    Colors.green[400]!,
                                                    Colors.green[600]!
                                                  ],
                                                  begin: Alignment.topLeft,
                                                  end: Alignment.bottomRight,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.green
                                                        .withOpacity(0.3),
                                                    blurRadius: 6,
                                                    offset: const Offset(0, 2),
                                                  ),
                                                ],
                                              ),
                                              child: Text(
                                                '${plan['discount'].toInt()}% OFF',
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 10,
                                                ),
                                              ),
                                            ),
                                          ),
                                        // Main content - centered with top padding to avoid badge overlap
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 8.0),
                                          child: Center(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                // Icon
                                                Icon(
                                                  Icons.calendar_month,
                                                  color: isSelected
                                                      ? Colors.white
                                                          .withOpacity(0.9)
                                                      : AppTheme.purple,
                                                  size: screenWidth < 400
                                                      ? 26
                                                      : 30,
                                                ),
                                                const SizedBox(height: 10),
                                                // Plan name
                                                Text(
                                                  plan['name'],
                                                  style: GoogleFonts.poppins(
                                                    fontSize: screenWidth < 400
                                                        ? 14
                                                        : 15,
                                                    fontWeight: FontWeight.w700,
                                                    color: isSelected
                                                        ? Colors.white
                                                            .withOpacity(0.9)
                                                        : AppTheme.textDark,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.visible,
                                                ),
                                                const SizedBox(height: 6),
                                                // Duration
                                                Text(
                                                  _getDurationDisplay(
                                                      plan['meals']),
                                                  style: GoogleFonts.poppins(
                                                    fontSize: screenWidth < 400
                                                        ? 12
                                                        : 13,
                                                    color: isSelected
                                                        ? Colors.white
                                                            .withOpacity(0.8)
                                                        : AppTheme.purple,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                // Per meal price
                                                if (perMealPrice > 0) ...[
                                                  const SizedBox(height: 6),
                                                  Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 8,
                                                        vertical: 3),
                                                    decoration: BoxDecoration(
                                                      color: isSelected
                                                          ? Colors.white
                                                              .withOpacity(0.2)
                                                          : Colors.grey[100],
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    child: Text(
                                                      '₹${perMealPrice.toStringAsFixed(0)}/meal',
                                                      style:
                                                          GoogleFonts.poppins(
                                                        fontSize:
                                                            screenWidth < 400
                                                                ? 11
                                                                : 12,
                                                        color: isSelected
                                                            ? Colors.white
                                                                .withOpacity(
                                                                    0.8)
                                                            : Colors.grey[700],
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                      textAlign:
                                                          TextAlign.center,
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ],
                                                // Selection indicator
                                                if (isSelected) ...[
                                                  const SizedBox(height: 8),
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.all(6),
                                                    decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      shape: BoxShape.circle,
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: Colors.black
                                                              .withOpacity(0.1),
                                                          blurRadius: 4,
                                                          offset: const Offset(
                                                              0, 2),
                                                        ),
                                                      ],
                                                    ),
                                                    child: Icon(
                                                      Icons.check,
                                                      color: AppTheme.purple,
                                                      size: 18,
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                      ),

                    const SizedBox(height: 12),

                    const SizedBox(height: 24),

                    // Plan mode selection
                    if (!widget.isExpressOrder &&
                        !(_getPlanData(_selectedPlanIndex)['isSingleDay'] ??
                            false))
                      Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.deepPurple.withOpacity(0.08),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Card(
                          margin: EdgeInsets.zero,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Delivery Days',
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textDark,
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade100,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () => _toggleCustomMode(),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 14),
                                            decoration: BoxDecoration(
                                              gradient: !_isCustomPlan
                                                  ? AppTheme.purpleToDeepPurple
                                                  : null,
                                              color: !_isCustomPlan
                                                  ? null
                                                  : Colors.transparent,
                                              borderRadius:
                                                  const BorderRadius.horizontal(
                                                left: Radius.circular(16),
                                              ),
                                              border: Border.all(
                                                color: !_isCustomPlan
                                                    ? Colors.transparent
                                                    : Colors.grey.shade300,
                                                width: 1.0,
                                              ),
                                              boxShadow: !_isCustomPlan
                                                  ? [
                                                      BoxShadow(
                                                        color: AppTheme.purple
                                                            .withOpacity(0.2),
                                                        blurRadius: 4,
                                                        offset:
                                                            const Offset(0, 2),
                                                      ),
                                                    ]
                                                  : null,
                                            ),
                                            child: Center(
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  if (!_isCustomPlan)
                                                    Container(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              4),
                                                      decoration:
                                                          const BoxDecoration(
                                                        color: Colors.white,
                                                        shape: BoxShape.circle,
                                                      ),
                                                      child: Icon(
                                                        Icons.check,
                                                        size: 12,
                                                        color: AppTheme.purple,
                                                      ),
                                                    ),
                                                  if (!_isCustomPlan)
                                                    const SizedBox(width: 8),
                                                  Text(
                                                    _deliveryDaysLabel
                                                            .isNotEmpty
                                                        ? _deliveryDaysLabel
                                                        : 'Mon to Fri',
                                                    style: GoogleFonts.poppins(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: !_isCustomPlan
                                                          ? Colors.white
                                                          : AppTheme.textMedium,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () => _toggleCustomMode(),
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 14),
                                            decoration: BoxDecoration(
                                              gradient: _isCustomPlan
                                                  ? AppTheme.purpleToDeepPurple
                                                  : null,
                                              color: _isCustomPlan
                                                  ? null
                                                  : Colors.transparent,
                                              borderRadius:
                                                  const BorderRadius.horizontal(
                                                right: Radius.circular(16),
                                              ),
                                              border: Border.all(
                                                color: _isCustomPlan
                                                    ? Colors.transparent
                                                    : Colors.grey.shade300,
                                                width: 1.0,
                                              ),
                                              boxShadow: _isCustomPlan
                                                  ? [
                                                      BoxShadow(
                                                        color: AppTheme.purple
                                                            .withOpacity(0.2),
                                                        blurRadius: 4,
                                                        offset:
                                                            const Offset(0, 2),
                                                      ),
                                                    ]
                                                  : null,
                                            ),
                                            child: Center(
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  if (_isCustomPlan)
                                                    Container(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              4),
                                                      decoration:
                                                          const BoxDecoration(
                                                        color: Colors.white,
                                                        shape: BoxShape.circle,
                                                      ),
                                                      child: Icon(
                                                        Icons.check,
                                                        size: 12,
                                                        color: AppTheme.purple,
                                                      ),
                                                    ),
                                                  if (_isCustomPlan)
                                                    const SizedBox(width: 8),
                                                  Text(
                                                    'Custom Days',
                                                    style: GoogleFonts.poppins(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: _isCustomPlan
                                                          ? Colors.white
                                                          : AppTheme.textMedium,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 16),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // Weekday selection for custom plan
                    if (!widget.isExpressOrder &&
                        !(_getPlanData(_selectedPlanIndex)['isSingleDay'] ??
                            false) &&
                        _isCustomPlan)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 16),
                          Text(
                            "Select Weekdays for Delivery",
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textDark,
                            ),
                          ),
                          const SizedBox(height: 12),
                          if (_deliveryDays.isNotEmpty)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 8.0),
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: List.generate(_deliveryDays.length,
                                      (index) {
                                    final day = _deliveryDays[index];
                                    // Map delivery day index to weekday index (0-6 for Mon-Sun)
                                    final weekdayIndex =
                                        _getWeekdayIndexFromDeliveryDay(day);
                                    final isSelected =
                                        weekdayIndex < _selectedWeekdays.length
                                            ? _selectedWeekdays[weekdayIndex]
                                            : false;
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 4.0),
                                      child: Stack(
                                        children: [
                                          ChoiceChip(
                                            label:
                                                Text(day['short_name'] ?? ''),
                                            selected: isSelected,
                                            onSelected: _isLoadingWorkingDays
                                                ? null
                                                : (selected) {
                                                    _handleWeekdaySelection(
                                                        weekdayIndex, selected);
                                                  },
                                            selectedColor: AppTheme.purple,
                                            backgroundColor: Colors.grey[200],
                                            labelStyle: TextStyle(
                                              color: isSelected
                                                  ? Colors.white
                                                  : Colors.black,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          // Loading indicator overlay
                                          if (_isLoadingWorkingDays)
                                            Positioned.fill(
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color: Colors.white
                                                      .withOpacity(0.7),
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                ),
                                                child: const Center(
                                                  child: SizedBox(
                                                    width: 16,
                                                    height: 16,
                                                    child:
                                                        CircularProgressIndicator(
                                                      strokeWidth: 2,
                                                      valueColor:
                                                          AlwaysStoppedAnimation<
                                                                  Color>(
                                                              AppTheme.purple),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    );
                                  }),
                                ),
                              ),
                            ),
                        ],
                      ),

                    const SizedBox(height: 16),

                    // Calendar view section
                    // Text(
                    //   "Schedule",
                    //   style: GoogleFonts.poppins(
                    //     fontSize: 18,
                    //     fontWeight: FontWeight.w600,
                    //     color: AppTheme.textDark,
                    //   ),
                    // ),

                    //const SizedBox(height: 0),

                    // New Schedule Card with fixed BoxShadow offset and vertical layout
                    Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Start date section - Shown for all plan modes
                            // if (!_isCustomPlan)
                            Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  size: 20,
                                  color: widget.isExpressOrder
                                      ? Colors.grey
                                      : AppTheme.purple,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            'Start Date',
                                            style: GoogleFonts.poppins(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                              color: AppTheme.textDark,
                                            ),
                                          ),
                                          if (!widget.isExpressOrder &&
                                              !_isCustomPlan)
                                            Text(
                                              ' *',
                                              style: GoogleFonts.poppins(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.red,
                                              ),
                                            ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        !_isCustomPlan &&
                                                !widget.isExpressOrder &&
                                                !_isStartDateSelected()
                                            ? "Tap 'Change' to select start date"
                                            : _isCustomPlan &&
                                                    _selectedWeekdays
                                                        .where((day) => day)
                                                        .isEmpty
                                                ? "No weekdays selected"
                                                : _getFormattedStartDate(),
                                        style: GoogleFonts.poppins(
                                          fontSize: 14,
                                          fontWeight: _isCustomPlan
                                              ? FontWeight.w500
                                              : FontWeight.normal,
                                          color: !_isCustomPlan &&
                                                  !widget.isExpressOrder &&
                                                  !_isStartDateSelected()
                                              ? Colors.orange
                                              : AppTheme.textDark,
                                          fontStyle: !_isCustomPlan &&
                                                  !widget.isExpressOrder &&
                                                  !_isStartDateSelected()
                                              ? FontStyle.italic
                                              : FontStyle.normal,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                      if (widget.isExpressOrder ||
                                          (_isCustomPlan &&
                                              !_selectedWeekdays
                                                  .where((day) => day)
                                                  .isEmpty))
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 4),
                                          child: Text(
                                            widget.isExpressOrder
                                                ? 'Locked for express delivery'
                                                : 'Based on earliest selected weekday',
                                            style: GoogleFonts.poppins(
                                              fontSize: 12,
                                              fontStyle: FontStyle.italic,
                                              color: widget.isExpressOrder
                                                  ? Colors.orange
                                                  : AppTheme.purple,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                if (!widget.isExpressOrder)
                                  TextButton.icon(
                                    onPressed: () async {
                                      print(
                                          'Change date button pressed. _enabledDates: _enabledDates');
                                      print(
                                          'DEBUG: _enabledDates count: ${_enabledDates.length}');
                                      print(
                                          'DEBUG: Enabled dates: ${_enabledDates.map((d) => "${d.day}/${d.month}/${d.year}").join(", ")}');
                                      print(
                                          'DEBUG: Current _startDate: ${_startDate.day}/${_startDate.month}/${_startDate.year}');
                                      if (_enabledDates.isEmpty) {
                                        ToastUtils.showToast(
                                          context: context,
                                          message:
                                              'No available dates to select.',
                                          type: ToastType.warning,
                                        );
                                        return;
                                      }
                                      // Find a valid initial date from enabled dates
                                      DateTime validInitialDate = _startDate;
                                      final isCurrentDateValid =
                                          _enabledDates.any((d) =>
                                              d.year == _startDate.year &&
                                              d.month == _startDate.month &&
                                              d.day == _startDate.day);

                                      if (!isCurrentDateValid &&
                                          _enabledDates.isNotEmpty) {
                                        // Find the first enabled date that's >= current start date
                                        final futureEnabledDates = _enabledDates
                                            .where((d) =>
                                                d.isAfter(_startDate) ||
                                                d.isAtSameMomentAs(_startDate))
                                            .toList();

                                        if (futureEnabledDates.isNotEmpty) {
                                          futureEnabledDates.sort();
                                          validInitialDate =
                                              futureEnabledDates.first;
                                        } else {
                                          // If no future dates, use the first available date
                                          final sortedDates =
                                              _enabledDates.toList()..sort();
                                          validInitialDate = sortedDates.first;
                                        }
                                      }

                                      print(
                                          'DEBUG: Using validInitialDate: ${validInitialDate.day}/${validInitialDate.month}/${validInitialDate.year}');

                                      final selectedDate = await showDatePicker(
                                        context: context,
                                        initialDate: validInitialDate,
                                        firstDate: _firstAvailableDate,
                                        lastDate: _lastAvailableDate,
                                        selectableDayPredicate:
                                            (DateTime date) {
                                          // Enable only dates present in _enabledDates (API working days)
                                          return _enabledDates.any((d) =>
                                              d.year == date.year &&
                                              d.month == date.month &&
                                              d.day == date.day);
                                        },
                                      );

                                      if (selectedDate != null) {
                                        setState(() {
                                          // For custom plan, use the smart start date calculation
                                          if (_isCustomPlan) {
                                            _startDate =
                                                calculateCustomPlanStartDate(
                                              selectedStartDate: selectedDate,
                                              selectedWeekdays:
                                                  _getSelectedWeekdayIndexes(),
                                            );
                                          } else {
                                            _startDate = selectedDate;
                                          }

                                          _focusedCalendarDate = _startDate;

                                          // If in custom mode, only allow selected weekdays
                                          if (_isCustomPlan &&
                                              !_selectedWeekdays[
                                                  selectedDate.weekday - 1]) {
                                            // Show an error message
                                            ToastUtils.showToast(
                                              context: context,
                                              message:
                                                  'Selected date doesn\'t match your weekday preferences. Adjusting selections.',
                                              type: ToastType.warning,
                                            );

                                            // Enable the selected weekday
                                            _selectedWeekdays[
                                                    selectedDate.weekday - 1] =
                                                true;
                                          }

                                          // Recalculate meal dates with new start date
                                          _calculateMealDates();
                                        });
                                      }
                                    },
                                    icon: Icon(
                                      Icons.edit_calendar,
                                      color: AppTheme.purple,
                                      size: 18,
                                    ),
                                    label: Text(
                                      'Change',
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppTheme.purple,
                                      ),
                                    ),
                                    style: TextButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8),
                                      minimumSize: const Size(60, 36),
                                    ),
                                  ),
                              ],
                            ),

                            // Divider between Start Date and End Date sections
                            Divider(
                                thickness: 1,
                                color: Colors.grey.withOpacity(0.1)),

                            // End date section
                            Row(
                              children: [
                                Icon(
                                  Icons.event_available,
                                  size: 20,
                                  color: AppTheme.purple,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'End Date',
                                        style: GoogleFonts.poppins(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          color: AppTheme.textDark,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              _endDate != null
                                                  ? DateFormat(
                                                          'EEEE, MMMM d, yyyy')
                                                      .format(_endDate!)
                                                  : 'Select at least one weekday',
                                              style: GoogleFonts.poppins(
                                                fontSize: 14,
                                                color: _endDate != null
                                                    ? AppTheme.textDark
                                                    : Colors.red,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                          if (_isCalculatingEndDate)
                                            const Padding(
                                              padding: EdgeInsets.only(left: 8),
                                              child: SizedBox(
                                                width: 16,
                                                height: 16,
                                                child:
                                                    CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                      if (_endDate != null)
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 4),
                                          child: Text(
                                            _endDateCalculationResponse != null
                                                ? 'Calculated via API'
                                                : 'Calculated locally (fallback)',
                                            style: GoogleFonts.poppins(
                                              fontSize: 12,
                                              fontStyle: FontStyle.italic,
                                              color:
                                                  _endDateCalculationResponse !=
                                                          null
                                                      ? Colors.green.shade600
                                                      : AppTheme.textMedium,
                                            ),
                                          ),
                                        ),
                                      if (_endDateCalculationError != null)
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 4),
                                          child: Text(
                                            'API Error: Using fallback calculation',
                                            style: GoogleFonts.poppins(
                                              fontSize: 12,
                                              fontStyle: FontStyle.italic,
                                              color: Colors.orange.shade600,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Calendar view with meal dates - Hidden in all scenarios
                    // Meal Schedule Card is completely removed/hidden in all scenarios
                    // No "if (false)" condition needed as we're completely removing it

                    const SizedBox(height: 16),

                    // Upcoming Meal Preview Section
                    if (false) // Hide Upcoming Meal Preview Section
                      Container(
                        margin: const EdgeInsets.symmetric(vertical: 16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.deepPurple.withOpacity(0.08),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Card(
                          margin: EdgeInsets.zero,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                        color: AppTheme.purple.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Icon(
                                        Icons.restaurant_rounded,
                                        color: AppTheme.purple,
                                        size: 22,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Upcoming Meal',
                                      style: GoogleFonts.poppins(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: AppTheme.textDark,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 20),
                                ..._getUpcomingMealDates()
                                    .map((date) => Container(
                                          margin:
                                              const EdgeInsets.only(bottom: 14),
                                          padding: const EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            border: Border.all(
                                              color: AppTheme.purple
                                                  .withOpacity(0.15),
                                              width: 1,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.grey
                                                    .withOpacity(0.07),
                                                blurRadius: 6,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.all(7),
                                                    decoration: BoxDecoration(
                                                      gradient: LinearGradient(
                                                        colors: [
                                                          AppTheme.purple
                                                              .withOpacity(0.1),
                                                          AppTheme.deepPurple
                                                              .withOpacity(0.1),
                                                        ],
                                                        begin:
                                                            Alignment.topLeft,
                                                        end: Alignment
                                                            .bottomRight,
                                                      ),
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: Icon(
                                                      Icons
                                                          .calendar_today_rounded,
                                                      color: AppTheme.purple,
                                                      size: 16,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(
                                                    DateFormat(
                                                            'EEE dd, MMM yyyy')
                                                        .format(date),
                                                    style: GoogleFonts.poppins(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: AppTheme.textDark,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 10),
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 2),
                                                    child: VegIcon(),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: Text(
                                                      _getMealItemsText(),
                                                      style:
                                                          GoogleFonts.poppins(
                                                        fontSize: 13,
                                                        color:
                                                            AppTheme.textMedium,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ))
                                    .toList(),
                                // Show message if no upcoming meals (for custom plan with no weekdays selected)
                                if (_getUpcomingMealDates().isEmpty)
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade50,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.grey.shade300,
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.info_outline,
                                          color: Colors.grey.shade600,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            'No upcoming meals. Please select at least one weekday.',
                                            style: GoogleFonts.poppins(
                                              fontSize: 14,
                                              color: Colors.grey.shade600,
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
          // Bottom section with chips and buttons
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Total amount and Continue button row
                Row(
                  children: [
                    // Total amount display on the left
                    Expanded(
                      flex: 1,
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          // Calculate responsive font sizes
                          final double maxWidth = constraints.maxWidth;
                          final bool isTablet = maxWidth > 600;

                          final double savingsFontSize = isTablet ? 14 : 11;
                          final double priceFontSize = isTablet ? 22 : 18;
                          final double originalPriceFontSize =
                              isTablet ? 16 : 13;
                          final double perDayFontSize = isTablet ? 14 : 11;

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Show savings amount for quarterly and annual plans
                              if (_getPlanData(_selectedPlanIndex)['discount'] >
                                  0)
                                Text(
                                  'Save ₹${_getSavings().toStringAsFixed(0)}',
                                  style: GoogleFonts.poppins(
                                    fontSize: savingsFontSize,
                                    color: Colors.green,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              // Total amount with original price for discounted plans
                              Wrap(
                                crossAxisAlignment: WrapCrossAlignment.end,
                                spacing: 8,
                                children: [
                                  Text(
                                    '₹${_getCombinedPrice().toStringAsFixed(0)}',
                                    style: GoogleFonts.poppins(
                                      fontSize: priceFontSize,
                                      fontWeight: FontWeight.w700,
                                      color: AppTheme.purple,
                                    ),
                                  ),
                                  if (_getPlanData(
                                          _selectedPlanIndex)['discount'] >
                                      0)
                                    Text(
                                      '₹${_calculateOriginalPrice().toStringAsFixed(0)}',
                                      style: GoogleFonts.poppins(
                                        fontSize: originalPriceFontSize,
                                        decoration: TextDecoration.lineThrough,
                                        color: Colors.grey[500],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                ],
                              ),
                              // Per day cost
                              Text(
                                '₹${(_getCombinedPrice() / (_mealDates.length > 0 ? _mealDates.length : 1)).toStringAsFixed(0)}/day',
                                style: GoogleFonts.poppins(
                                  fontSize: perDayFontSize,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Continue button on the right
                    Expanded(
                      flex: 2,
                      child: Container(
                        height: 60,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50),
                          gradient: AppTheme.purpleToDeepPurple,
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.deepPurple.withOpacity(0.3),
                              blurRadius: 16,
                              offset: const Offset(0, 6),
                              spreadRadius: -4,
                            ),
                          ],
                        ),
                        child: ElevatedButton(
                          onPressed: _endDate != null &&
                                  !(widget.isExpressOrder &&
                                      !MealPlanValidator
                                          .isWithinExpressWindow())
                              ? () => _navigateToOrderSummary(context)
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            foregroundColor: Colors.white,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(50),
                            ),
                            padding: EdgeInsets.zero,
                            disabledBackgroundColor: Colors.transparent,
                            disabledForegroundColor:
                                Colors.white.withOpacity(0.6),
                            elevation: 0,
                          ),
                          child: Text(
                            widget.isExpressOrder
                                ? (MealPlanValidator.isWithinExpressWindow()
                                    ? 'Continue'
                                    : 'Express Orders Unavailable')
                                : 'Continue',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeekdayCircle(String text, bool isSelected,
      {bool disabled = false}) {
    return GestureDetector(
      onTap: disabled
          ? null
          : () {
              int index = _weekdayNames.indexWhere((day) =>
                  day.substring(0, 1).toUpperCase() == text.toUpperCase());
              if (index != -1) {
                setState(() {
                  _selectedWeekdays[index] = !_selectedWeekdays[index];
                  _calculateMealDates();
                });
              }
            },
      child: Container(
        width: 42,
        height: 42,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient:
              isSelected && !disabled ? AppTheme.purpleToDeepPurple : null,
          color: isSelected
              ? null
              : disabled
                  ? Colors.grey.shade200
                  : Colors.white,
          boxShadow: isSelected && !disabled
              ? [
                  BoxShadow(
                    color: AppTheme.deepPurple.withOpacity(0.2),
                    blurRadius: 6,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  )
                ]
              : [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.15),
                    blurRadius: 4,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  )
                ],
          border: Border.all(
            color: isSelected && !disabled
                ? Colors.transparent
                : disabled
                    ? Colors.grey.shade300
                    : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            text,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected && !disabled
                  ? Colors.white
                  : disabled
                      ? Colors.grey.shade500
                      : AppTheme.textMedium,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWeekdayCircleViewOnly(String text, bool isSelected,
      {bool disabled = false}) {
    return Container(
      width: 42,
      height: 42,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: isSelected && !disabled ? AppTheme.purpleToDeepPurple : null,
        color: isSelected
            ? null
            : disabled
                ? Colors.grey.shade200
                : Colors.white,
        boxShadow: isSelected && !disabled
            ? [
                BoxShadow(
                  color: AppTheme.deepPurple.withOpacity(0.2),
                  blurRadius: 6,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                )
              ]
            : [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.15),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                )
              ],
        border: Border.all(
          color: isSelected && !disabled
              ? Colors.transparent
              : disabled
                  ? Colors.grey.shade300
                  : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          text,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected && !disabled
                ? Colors.white
                : disabled
                    ? Colors.grey.shade500
                    : AppTheme.textMedium,
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {TextStyle? valueStyle}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppTheme.textMedium,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: valueStyle ??
                  GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppTheme.textDark,
                  ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpressOnlyPlanCard() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: AppTheme.deepPurple.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Card(
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: AppTheme.purple,
            width: 1.5,
          ),
        ),
        elevation: 0,
        child: InkWell(
          onTap: () => _selectPlan(0),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: [
                    AppTheme.purple.withOpacity(0.05),
                    AppTheme.deepPurple.withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: AppTheme.purpleToDeepPurple,
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 14,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Single Day',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textDark,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              color: AppTheme.orange.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: AppTheme.orange.withOpacity(0.5),
                              ),
                            ),
                            child: Text(
                              'EXPRESS',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.orange,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Text(
                        '1 Day • ${_getDisplayMealCount(1)} meal${_getDisplayMealCount(1) > 1 ? 's' : ''}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: AppTheme.textMedium,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '₹${widget.totalMealCost.toStringAsFixed(0)}',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.purple,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build calendar legend item
  Widget _buildCalendarLegendItem(
    String label,
    Color color, {
    Color? backgroundColor,
    bool hasBorder = false,
    Color? borderColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor ?? color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: hasBorder
            ? Border.all(
                color: borderColor ?? color.withOpacity(0.3),
                width: 1,
              )
            : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced summary row with icon and modern styling
  Widget _buildEnhancedSummaryRow(
    String label,
    String value,
    IconData icon, {
    TextStyle? valueStyle,
    bool showIcon = true,
    bool isMultiline = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment:
            isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          if (showIcon) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.purple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                size: 16,
                color: AppTheme.purple,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textDark,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 3,
            child: isMultiline
                ? Tooltip(
                    message: value,
                    waitDuration: const Duration(milliseconds: 500),
                    showDuration: const Duration(seconds: 2),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.purple.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    textStyle: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                    child: Text(
                      value,
                      style: valueStyle ??
                          GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textDark,
                          ),
                      textAlign: TextAlign.right,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  )
                : Text(
                    value,
                    style: valueStyle ??
                        GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textDark,
                        ),
                    textAlign: TextAlign.right,
                  ),
          ),
        ],
      ),
    );
  }

  // Special builder for Selected Days row to handle potential overflow
  Widget _buildSelectedDaysRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppTheme.textMedium,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Tooltip(
              message: value, // Show full text in tooltip
              waitDuration: const Duration(milliseconds: 500),
              showDuration: const Duration(seconds: 2),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.purple.withOpacity(0.9),
                borderRadius: BorderRadius.circular(8),
              ),
              textStyle: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 12,
              ),
              child: Text(
                value,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppTheme.textDark,
                ),
                textAlign: TextAlign.right,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandableMealScheduleCard() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with icon, title and arrow
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  _isMealScheduleExpanded = !_isMealScheduleExpanded;
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _isMealScheduleExpanded
                        ? AppTheme.purple.withOpacity(0.3)
                        : Colors.transparent,
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  color: _isMealScheduleExpanded
                      ? AppTheme.purple.withOpacity(0.03)
                      : Colors.transparent,
                ),
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppTheme.purple.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.calendar_month_rounded,
                        color: AppTheme.purple,
                        size: 22,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Meal Schedule',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textDark,
                            ),
                          ),
                          const SizedBox(height: 4),
                          if (!(_getPlanData(
                                  _selectedPlanIndex)['isSingleDay'] ??
                              false))
                            Text(
                              _isCustomPlan
                                  ? 'Delivery Days: ${_getSelectedWeekdaysText()}'
                                  : 'Delivery Days: Monday to Friday',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: AppTheme.textMedium,
                              ),
                            ),
                          if (!_isMealScheduleExpanded)
                            Padding(
                              padding: const EdgeInsets.only(top: 4.0),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.touch_app,
                                    size: 14,
                                    color: AppTheme.purple.withOpacity(0.7),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Tap to view calendar',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      fontStyle: FontStyle.italic,
                                      color: AppTheme.purple.withOpacity(0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                    AnimatedRotation(
                      turns: _isMealScheduleExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 300),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _isMealScheduleExpanded
                              ? AppTheme.purple.withOpacity(0.1)
                              : Colors.grey.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: _isMealScheduleExpanded
                              ? AppTheme.purple
                              : Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Animated container for expanding/collapsing content
          ClipRect(
            child: AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: _isMealScheduleExpanded
                  ? Container(
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(
                            color: AppTheme.purple.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                      ),
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.only(left: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 12),
                          // Calendar with loading overlay for comprehensive month navigation
                          Container(
                            constraints: BoxConstraints(
                              maxHeight:
                                  MediaQuery.of(context).size.width * 0.9,
                            ),
                            child: CustomCalendar(
                              firstDay: _firstAvailableDate,
                              lastDay: _lastAvailableDate,
                              focusedDay: _ensureValidFocusedDay(),
                              selectedDay: _startDate,
                              enabledDates: _enabledDates,
                              isLoading: _isLoadingWorkingDays,
                              onDaySelected: widget.isExpressOrder
                                  ? (DateTime date) {}
                                  : (DateTime date) =>
                                      _onDaySelected(date, date),
                              onMonthChanged: (month) {
                                print(
                                    '🔥🔥🔥 SUBSCRIPTION SCREEN: onMonthChanged called for ${month.month}/${month.year} 🔥🔥🔥');
                                log('📅 🚀 Custom Calendar: Month changed to ${month.month}/${month.year}');
                                log('📅 Current _focusedCalendarDate before update: ${_focusedCalendarDate?.month}/${_focusedCalendarDate?.year}');
                                print('🔥 CALLING _handleMonthNavigation...');
                                _handleMonthNavigation(month, 'CustomCalendar');
                                print('🔥 _handleMonthNavigation COMPLETED');
                              },
                              enabledDayPredicate: (day) {
                                // For express orders, only enable the selected day and disable all others
                                if (widget.isExpressOrder) {
                                  return day.year == _startDate.year &&
                                      day.month == _startDate.month &&
                                      day.day == _startDate.day;
                                }

                                // Check if date is in the past
                                final now = DateTime.now();
                                final today =
                                    DateTime(now.year, now.month, now.day);
                                final dayOnly =
                                    DateTime(day.year, day.month, day.day);

                                if (dayOnly.isBefore(today)) {
                                  return false;
                                }

                                // Priority 1: Use real-time working days API data if available
                                if (_enabledDates.isNotEmpty) {
                                  final isEnabled =
                                      _enabledDates.any((enabledDate) {
                                    final enabledDateOnly = DateTime(
                                        enabledDate.year,
                                        enabledDate.month,
                                        enabledDate.day);
                                    return enabledDateOnly == dayOnly;
                                  });

                                  // Log for debugging comprehensive month navigation
                                  if (isEnabled) {
                                    // Only log occasionally to avoid spam
                                    if (day.day == 1 || day.day == 15) {
                                      log('✅ Date ${day.day}/${day.month}/${day.year} enabled via API data');
                                    }
                                  }

                                  return isEnabled;
                                }

                                // Priority 2: During loading, show minimal dates to indicate loading state
                                if (_isLoadingWorkingDays) {
                                  // Show only first few weekdays to indicate loading
                                  return day.weekday <= 5 && day.day <= 7;
                                }

                                // Priority 3: Fallback to weekdays only if API data is not available yet
                                return day.weekday <= 5;
                              },
                            ),
                          ),
                          // Calendar legend/hint
                          Padding(
                            padding:
                                const EdgeInsets.only(top: 16.0, bottom: 8),
                            child: Row(
                              children: [
                                Icon(Icons.info_outline,
                                    size: 16, color: AppTheme.textMedium),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Dots indicate days with scheduled meal deliveries',
                                    style: GoogleFonts.poppins(
                                      fontSize: 13,
                                      fontStyle: FontStyle.italic,
                                      color: AppTheme.textMedium,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  : Container(),
            ),
          ),
        ],
      ),
    );
  }

  // Add method to check if chip should be shown
  bool _shouldShowMealChip() {
    if (!_isStartDateSelected()) return false;
    if (_endDate == null) return false;
    return true;
  }

  // Get display meal count (multiply by 2 if both breakfast and lunch are selected)
  int _getDisplayMealCount(int baseMealCount) {
    if (widget.mealType == 'breakfast_and_lunch') {
      return baseMealCount * 2;
    }
    return baseMealCount;
  }
}
