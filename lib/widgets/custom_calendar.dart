import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:startwell/themes/app_theme.dart';
import 'dart:developer';

/// Custom Calendar Widget with full control over month navigation and API integration
class CustomCalendar extends StatefulWidget {
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime focusedDay;
  final DateTime? selectedDay;
  final Set<DateTime> enabledDates;
  final bool isLoading;
  final Function(DateTime) onDaySelected;
  final Function(DateTime) onMonthChanged;
  final bool Function(DateTime) enabledDayPredicate;

  const CustomCalendar({
    Key? key,
    required this.firstDay,
    required this.lastDay,
    required this.focusedDay,
    this.selectedDay,
    required this.enabledDates,
    this.isLoading = false,
    required this.onDaySelected,
    required this.onMonthChanged,
    required this.enabledDayPredicate,
  }) : super(key: key);

  @override
  State<CustomCalendar> createState() => _CustomCalendarState();
}

class _CustomCalendarState extends State<CustomCalendar> {
  late DateTime _currentMonth;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentMonth =
        DateTime(widget.focusedDay.year, widget.focusedDay.month, 1);
    _pageController = PageController(
      initialPage: _getPageIndex(_currentMonth),
    );
    log('📅 🚀 Custom Calendar initialized');
    log('📅 Initial focused day: ${widget.focusedDay}');
    log('📅 Initial current month: ${_currentMonth.month}/${_currentMonth.year}');
    log('📅 First allowed day: ${widget.firstDay}');
    log('📅 Last allowed day: ${widget.lastDay}');
    log('📅 Initial page index: ${_getPageIndex(_currentMonth)}');
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Calculate page index for a given month
  int _getPageIndex(DateTime month) {
    final firstMonth = DateTime(widget.firstDay.year, widget.firstDay.month, 1);
    return (month.year - firstMonth.year) * 12 +
        (month.month - firstMonth.month);
  }

  /// Get month from page index
  DateTime _getMonthFromIndex(int index) {
    final firstMonth = DateTime(widget.firstDay.year, widget.firstDay.month, 1);
    final year = firstMonth.year + (index ~/ 12);
    final month = firstMonth.month + (index % 12);
    return DateTime(year, month, 1);
  }

  /// Navigate to previous month with enhanced feedback
  void _previousMonth() {
    print('🔥🔥🔥 PREVIOUS MONTH ARROW CLICKED! 🔥🔥🔥');
    log('🔥🔥🔥 PREVIOUS MONTH ARROW CLICKED! 🔥🔥🔥');

    // Prevent navigation if already loading
    if (widget.isLoading) {
      log('📅 ⏸️ Previous month navigation blocked - API call in progress');
      return;
    }

    log('📅 ⬅️ Custom Calendar: Previous month arrow clicked');
    log('📅 Current month: ${_currentMonth.month}/${_currentMonth.year}');
    final newMonth = DateTime(_currentMonth.year, _currentMonth.month - 1, 1);
    log('📅 Target month: ${newMonth.month}/${newMonth.year}');

    final firstAllowed =
        DateTime(widget.firstDay.year, widget.firstDay.month, 1);
    log('📅 First allowed: ${firstAllowed.month}/${firstAllowed.year}');

    if (!newMonth.isBefore(firstAllowed)) {
      log('📅 ✅ Previous month navigation allowed - calling _navigateToMonth');
      print(
          '🔥 CALLING _navigateToMonth for previous month: ${newMonth.month}/${newMonth.year}');
      _navigateToMonth(newMonth);
    } else {
      log('📅 ❌ Previous month navigation blocked - before first allowed date');
      print('🔥 Previous month navigation BLOCKED - before first allowed date');
      _showNavigationFeedback('Cannot go to previous month');
    }
  }

  /// Navigate to next month with enhanced feedback
  void _nextMonth() {
    print('🔥🔥🔥 NEXT MONTH ARROW CLICKED! 🔥🔥🔥');
    log('🔥🔥🔥 NEXT MONTH ARROW CLICKED! 🔥🔥🔥');

    // Prevent navigation if already loading
    if (widget.isLoading) {
      log('📅 ⏸️ Next month navigation blocked - API call in progress');
      return;
    }

    log('📅 ➡️ Custom Calendar: Next month arrow clicked');
    log('📅 Current month: ${_currentMonth.month}/${_currentMonth.year}');
    final newMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 1);
    log('📅 Target month: ${newMonth.month}/${newMonth.year}');

    final lastAllowed = DateTime(widget.lastDay.year, widget.lastDay.month, 1);
    log('📅 Last allowed: ${lastAllowed.month}/${lastAllowed.year}');

    if (!newMonth.isAfter(lastAllowed)) {
      log('📅 ✅ Next month navigation allowed - calling _navigateToMonth');
      print(
          '🔥 CALLING _navigateToMonth for next month: ${newMonth.month}/${newMonth.year}');
      _navigateToMonth(newMonth);
    } else {
      log('📅 ❌ Next month navigation blocked - after last allowed date');
      print('🔥 Next month navigation BLOCKED - after last allowed date');
      _showNavigationFeedback('Cannot go to next month');
    }
  }

  /// Show brief feedback for navigation attempts
  void _showNavigationFeedback(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  /// Navigate to specific month with API integration
  void _navigateToMonth(DateTime month) {
    print(
        '🔥🔥🔥 _navigateToMonth CALLED for ${month.month}/${month.year} 🔥🔥🔥');
    log('📅 🚀 Custom Calendar: Navigating to ${month.month}/${month.year}');
    log('📅 🔄 Updating _currentMonth state...');
    setState(() {
      _currentMonth = month;
    });
    log('📅 ✅ State updated to: ${_currentMonth.month}/${_currentMonth.year}');

    // Trigger API call for new month
    log('📅 🌐 Triggering API call via widget.onMonthChanged...');
    print(
        '🔥🔥🔥 CALLING widget.onMonthChanged(${month.month}/${month.year}) 🔥🔥🔥');
    widget.onMonthChanged(month);
    print('🔥🔥🔥 widget.onMonthChanged CALLED SUCCESSFULLY 🔥🔥🔥');

    // Update page controller
    final pageIndex = _getPageIndex(month);
    log('📅 📄 Animating to page index: $pageIndex');
    _pageController.animateToPage(
      pageIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    log('📅 ✅ Navigation completed');
  }

  @override
  Widget build(BuildContext context) {
    print('🔥🔥🔥 CUSTOM CALENDAR BUILD() CALLED! 🔥🔥🔥');
    print(
        '🔥 Current month in build: ${_currentMonth.month}/${_currentMonth.year}');
    return Focus(
      onKeyEvent: _handleKeyEvent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Column(
              children: [
                _buildHeader(),
                _buildWeekdayHeaders(),
                _buildCalendarGrid(),
              ],
            ),
            if (widget.isLoading) _buildLoadingOverlay(),
          ],
        ),
      ),
    );
  }

  /// Handle keyboard navigation
  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
        _previousMonth();
        return KeyEventResult.handled;
      } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
        _nextMonth();
        return KeyEventResult.handled;
      }
    }
    return KeyEventResult.ignored;
  }

  /// Build calendar header with enhanced navigation arrows
  Widget _buildHeader() {
    final canGoPrevious = !_currentMonth.isBefore(
      DateTime(widget.firstDay.year, widget.firstDay.month, 1),
    );
    final canGoNext = !_currentMonth.isAfter(
      DateTime(widget.lastDay.year, widget.lastDay.month, 1),
    );

    print('🔥🔥🔥 HEADER BUILD DEBUG 🔥🔥🔥');
    print('🔥 Current month: ${_currentMonth.month}/${_currentMonth.year}');
    print('🔥 First day: ${widget.firstDay}');
    print('🔥 Last day: ${widget.lastDay}');
    print('🔥 canGoPrevious: $canGoPrevious');
    print('🔥 canGoNext: $canGoNext');
    print('🔥 isLoading: ${widget.isLoading}');

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppTheme.purple.withOpacity(0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Enhanced Previous month button
          _buildNavigationButton(
            icon: Icons.chevron_left,
            onPressed: canGoPrevious ? _previousMonth : null,
            enabled: canGoPrevious,
            tooltip: canGoPrevious
                ? 'Previous month'
                : 'Cannot go to previous month',
            isLoading: widget.isLoading,
          ),

          // Month/Year title with loading indicator
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _getMonthYearString(_currentMonth),
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textDark,
                ),
              ),
              if (widget.isLoading) ...[
                const SizedBox(width: 8),
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.purple),
                  ),
                ),
              ],
            ],
          ),

          // Enhanced Next month button
          _buildNavigationButton(
            icon: Icons.chevron_right,
            onPressed: canGoNext ? _nextMonth : null,
            enabled: canGoNext,
            tooltip: canGoNext ? 'Next month' : 'Cannot go to next month',
            isLoading: widget.isLoading,
          ),
        ],
      ),
    );
  }

  /// Build enhanced navigation button with better UX
  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool enabled,
    required String tooltip,
    required bool isLoading,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: enabled && !isLoading
              ? AppTheme.purple.withOpacity(0.1)
              : Colors.transparent,
          border: enabled && !isLoading
              ? Border.all(color: AppTheme.purple.withOpacity(0.2))
              : null,
        ),
        child: IconButton(
          onPressed: enabled && !isLoading
              ? () {
                  print(
                      '🔥🔥🔥 ICON BUTTON PRESSED! enabled=$enabled, isLoading=$isLoading 🔥🔥🔥');
                  if (onPressed != null) {
                    onPressed!();
                  } else {
                    print('🔥 ERROR: onPressed is null!');
                  }
                }
              : null,
          icon: Icon(
            icon,
            color:
                enabled && !isLoading ? AppTheme.purple : Colors.grey.shade400,
            size: 28,
          ),
          splashRadius: 24,
          padding: const EdgeInsets.all(8),
        ),
      ),
    );
  }

  /// Build weekday headers (Mon, Tue, Wed, etc.)
  Widget _buildWeekdayHeaders() {
    const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: weekdays
            .map((day) => Expanded(
                  child: Center(
                    child: Text(
                      day,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textMedium,
                      ),
                    ),
                  ),
                ))
            .toList(),
      ),
    );
  }

  /// Build the main calendar grid
  Widget _buildCalendarGrid() {
    return Container(
      height: 300, // Fixed height for calendar grid
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          final month = _getMonthFromIndex(index);
          log('📅 🔄 Custom Calendar: PageView changed to ${month.month}/${month.year}');
          setState(() {
            _currentMonth = month;
          });
          widget.onMonthChanged(month);
        },
        itemBuilder: (context, index) {
          final month = _getMonthFromIndex(index);
          return _buildMonthGrid(month);
        },
      ),
    );
  }

  /// Build grid for a specific month
  Widget _buildMonthGrid(DateTime month) {
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final firstWeekday = firstDayOfMonth.weekday; // 1 = Monday, 7 = Sunday

    // Calculate grid layout
    final totalCells = 42; // 6 rows × 7 days
    final List<Widget> dayWidgets = [];

    // Add empty cells for days before the first day of the month
    for (int i = 1; i < firstWeekday; i++) {
      dayWidgets.add(Container());
    }

    // Add day cells for the current month
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(month.year, month.month, day);
      dayWidgets.add(_buildDayCell(date));
    }

    // Fill remaining cells
    while (dayWidgets.length < totalCells) {
      dayWidgets.add(Container());
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: GridView.count(
        crossAxisCount: 7,
        children: dayWidgets,
      ),
    );
  }

  /// Build individual day cell
  Widget _buildDayCell(DateTime date) {
    final isEnabled = widget.enabledDayPredicate(date);
    final isSelected = widget.selectedDay != null &&
        date.year == widget.selectedDay!.year &&
        date.month == widget.selectedDay!.month &&
        date.day == widget.selectedDay!.day;
    final isToday = _isToday(date);

    return GestureDetector(
      onTap: isEnabled ? () => widget.onDaySelected(date) : null,
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.purple
              : isToday
                  ? AppTheme.purple.withOpacity(0.1)
                  : Colors.transparent,
          shape: BoxShape.circle,
          border: isToday && !isSelected
              ? Border.all(color: AppTheme.purple, width: 1)
              : null,
        ),
        child: Center(
          child: Text(
            '${date.day}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              color: isSelected
                  ? Colors.white
                  : isEnabled
                      ? (date.weekday >= 6
                          ? AppTheme.error.withOpacity(0.7)
                          : AppTheme.textDark)
                      : Colors.grey.shade400,
            ),
          ),
        ),
      ),
    );
  }

  /// Build loading overlay
  Widget _buildLoadingOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.purple),
              ),
              SizedBox(height: 12),
              Text(
                'Loading working days...',
                style: TextStyle(
                  color: AppTheme.textMedium,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get month/year string for display
  String _getMonthYearString(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  /// Check if date is today
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }
}
