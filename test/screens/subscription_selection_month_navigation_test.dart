import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:startwell/screens/subscription_selection_screen.dart';
import 'package:startwell/services/working_days_service.dart';
import 'dart:developer' as dev;

void main() {
  group('Subscription Selection Month Navigation Tests', () {
    group('Month Navigation API Integration', () {
      test('should format month parameter correctly for API calls', () {
        // Test month formatting for different dates
        final testCases = [
          {'input': DateTime(2025, 1, 15), 'expected': '2025-01'},
          {'input': DateTime(2025, 12, 31), 'expected': '2025-12'},
          {'input': DateTime(2024, 7, 1), 'expected': '2024-07'},
        ];

        for (final testCase in testCases) {
          final input = testCase['input'] as DateTime;
          final expected = testCase['expected'] as String;

          // Test the date formatting used in WorkingDaysService
          final formatted =
              '${input.year.toString().padLeft(4, '0')}-${input.month.toString().padLeft(2, '0')}';
          expect(formatted, equals(expected));
        }
      });

      test('should preserve default weekdays (Mon-Fri) during month navigation',
          () {
        // Test default delivery days
        final defaultDays = WorkingDaysService.getDefaultDeliveryDays();
        expect(defaultDays, equals([1, 2, 3, 4, 5])); // Monday to Friday

        // Verify this matches the expected API parameter format
        final expectedApiParams =
            'days[]=1&days[]=2&days[]=3&days[]=4&days[]=5';
        final actualApiParams =
            defaultDays.map((day) => 'days[]=$day').join('&');
        expect(actualApiParams, equals(expectedApiParams));
      });

      test('should preserve custom selected days during month navigation', () {
        // Test custom weekday selection (Mon, Wed, Fri)
        final selectedWeekdays = [true, false, true, false, true, false, false];
        final customDays =
            WorkingDaysService.convertSelectedWeekdaysToCustomDays(
                selectedWeekdays);

        expect(customDays, equals([1, 3, 5])); // Monday, Wednesday, Friday

        // Verify API parameter format
        final expectedApiParams = 'days[]=1&days[]=3&days[]=5';
        final actualApiParams =
            customDays.map((day) => 'days[]=$day').join('&');
        expect(actualApiParams, equals(expectedApiParams));
      });

      test('should handle edge cases for custom day selection', () {
        // Test Sunday selection (Sunday = 0 in API)
        final sundaySelection = [
          false,
          false,
          false,
          false,
          false,
          false,
          true
        ];
        final sundayDays =
            WorkingDaysService.convertSelectedWeekdaysToCustomDays(
                sundaySelection);
        expect(sundayDays, equals([0]));

        // Test all days selected
        final allDaysSelection = [true, true, true, true, true, true, true];
        final allDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(
            allDaysSelection);
        expect(allDays, equals([1, 2, 3, 4, 5, 6, 0])); // Mon-Sat, then Sunday

        // Test no days selected
        final noDaysSelection = [
          false,
          false,
          false,
          false,
          false,
          false,
          false
        ];
        final noDays = WorkingDaysService.convertSelectedWeekdaysToCustomDays(
            noDaysSelection);
        expect(noDays, isEmpty);
      });
    });

    group('API URL Construction', () {
      test('should construct correct API URL for default weekdays', () {
        const baseUrl = 'http://192.168.1.167:8005/api/v2/admin/settings';
        const companyId = 8163;
        const kitchenId = 1;
        const mealType = 'lunch';
        const monthSelected = '2025-07';
        final defaultDays = [1, 2, 3, 4, 5];

        final expectedUrl =
            '$baseUrl/working-days?company_id=$companyId&month_selected=$monthSelected&kitchen_id=$kitchenId&meal_type=$mealType&days[]=1&days[]=2&days[]=3&days[]=4&days[]=5';

        // Construct URL parts as done in WorkingDaysService
        final queryParts = [
          'company_id=$companyId',
          'month_selected=$monthSelected',
          'kitchen_id=$kitchenId',
          'meal_type=$mealType',
        ];

        for (int day in defaultDays) {
          queryParts.add('days[]=$day');
        }

        final actualUrl = '$baseUrl/working-days?${queryParts.join('&')}';
        expect(actualUrl, equals(expectedUrl));
      });

      test('should construct correct API URL for custom weekdays', () {
        const baseUrl = 'http://192.168.1.167:8005/api/v2/admin/settings';
        const companyId = 8163;
        const kitchenId = 1;
        const mealType = 'lunch';
        const monthSelected = '2025-08';
        final customDays = [1, 3, 5]; // Monday, Wednesday, Friday

        final expectedUrl =
            '$baseUrl/working-days?company_id=$companyId&month_selected=$monthSelected&kitchen_id=$kitchenId&meal_type=$mealType&days[]=1&days[]=3&days[]=5';

        final queryParts = [
          'company_id=$companyId',
          'month_selected=$monthSelected',
          'kitchen_id=$kitchenId',
          'meal_type=$mealType',
        ];

        for (int day in customDays) {
          queryParts.add('days[]=$day');
        }

        final actualUrl = '$baseUrl/working-days?${queryParts.join('&')}';
        expect(actualUrl, equals(expectedUrl));
      });
    });

    group('Month Navigation Scenarios', () {
      test('should handle month navigation from current month to next month',
          () {
        final currentMonth = DateTime(2025, 7, 15);
        final nextMonth = DateTime(2025, 8, 15);

        // Verify month formatting changes correctly
        final currentFormatted =
            '${currentMonth.year}-${currentMonth.month.toString().padLeft(2, '0')}';
        final nextFormatted =
            '${nextMonth.year}-${nextMonth.month.toString().padLeft(2, '0')}';

        expect(currentFormatted, equals('2025-07'));
        expect(nextFormatted, equals('2025-08'));
      });

      test('should handle year boundary month navigation', () {
        final decemberMonth = DateTime(2025, 12, 15);
        final januaryMonth = DateTime(2026, 1, 15);

        final decemberFormatted =
            '${decemberMonth.year}-${decemberMonth.month.toString().padLeft(2, '0')}';
        final januaryFormatted =
            '${januaryMonth.year}-${januaryMonth.month.toString().padLeft(2, '0')}';

        expect(decemberFormatted, equals('2025-12'));
        expect(januaryFormatted, equals('2026-01'));
      });
    });

    group('Loading State Management', () {
      test('should manage loading states correctly during month transitions',
          () {
        // This test verifies the expected loading state flow
        // In actual implementation:
        // 1. _isLoadingWorkingDays = true (immediately)
        // 2. _enabledDates = {} (clear calendar)
        // 3. API call
        // 4. _isLoadingWorkingDays = false (on completion)

        bool isLoading = false;
        Set<DateTime> enabledDates = {
          DateTime(2025, 7, 22),
          DateTime(2025, 7, 23)
        };

        // Simulate month navigation start
        isLoading = true;
        enabledDates = {};

        expect(isLoading, isTrue);
        expect(enabledDates, isEmpty);

        // Simulate API response
        isLoading = false;
        enabledDates = {DateTime(2025, 8, 22), DateTime(2025, 8, 23)};

        expect(isLoading, isFalse);
        expect(enabledDates, isNotEmpty);
        expect(enabledDates.length, equals(2));
      });
    });

    group('Error Handling', () {
      test('should handle API errors gracefully during month navigation', () {
        // Test error state management
        String? workingDaysError;
        bool isLoading = false;
        Set<DateTime> enabledDates = {};

        // Simulate API error
        workingDaysError =
            'Failed to load working days for 2025-08: Network error';
        isLoading = false;

        // Should provide fallback dates (weekdays only)
        final fallbackDates = <DateTime>{};
        final now = DateTime.now();
        for (int i = 0; i < 30; i++) {
          final date = now.add(Duration(days: i));
          if (date.weekday >= 1 && date.weekday <= 5) {
            // Monday to Friday
            fallbackDates.add(date);
          }
        }
        enabledDates = fallbackDates;

        expect(workingDaysError, isNotNull);
        expect(workingDaysError, contains('Failed to load working days'));
        expect(isLoading, isFalse);
        expect(enabledDates, isNotEmpty);
      });
    });
  });

  group('Arrow Navigation Enhancement Tests', () {
    test('should handle loading state during arrow navigation', () {
      // Test that arrows are disabled during loading
      bool isLoading = true;
      bool canGoPrevious = true;
      bool canGoNext = true;

      // During loading, navigation should be blocked
      bool shouldAllowPrevious = canGoPrevious && !isLoading;
      bool shouldAllowNext = canGoNext && !isLoading;

      expect(shouldAllowPrevious, isFalse);
      expect(shouldAllowNext, isFalse);

      // After loading completes
      isLoading = false;
      shouldAllowPrevious = canGoPrevious && !isLoading;
      shouldAllowNext = canGoNext && !isLoading;

      expect(shouldAllowPrevious, isTrue);
      expect(shouldAllowNext, isTrue);
    });

    test('should provide correct tooltip messages', () {
      // Test tooltip logic
      bool canGoPrevious = true;
      bool canGoNext = false;

      String previousTooltip =
          canGoPrevious ? 'Previous month' : 'Cannot go to previous month';
      String nextTooltip = canGoNext ? 'Next month' : 'Cannot go to next month';

      expect(previousTooltip, equals('Previous month'));
      expect(nextTooltip, equals('Cannot go to next month'));
    });

    test('should handle boundary conditions correctly', () {
      // Test first month boundary
      final firstAllowed = DateTime(2025, 7, 1);
      final currentMonth = DateTime(2025, 7, 1);
      final previousMonth =
          DateTime(currentMonth.year, currentMonth.month - 1, 1);

      bool canGoPrevious = !previousMonth.isBefore(firstAllowed);
      expect(canGoPrevious, isFalse);

      // Test last month boundary
      final lastAllowed = DateTime(2025, 12, 31);
      final lastAllowedMonth = DateTime(lastAllowed.year, lastAllowed.month, 1);
      final currentMonth2 = DateTime(2025, 12, 1);
      final nextMonth =
          DateTime(currentMonth2.year, currentMonth2.month + 1, 1);

      bool canGoNext = !nextMonth.isAfter(lastAllowedMonth);
      expect(canGoNext, isFalse);
    });

    test('should calculate page indices correctly for navigation', () {
      final firstDay = DateTime(2025, 1, 1);
      final targetMonth = DateTime(2025, 7, 1);

      // Calculate page index as done in CustomCalendar
      final firstMonth = DateTime(firstDay.year, firstDay.month, 1);
      final pageIndex = (targetMonth.year - firstMonth.year) * 12 +
          (targetMonth.month - firstMonth.month);

      expect(pageIndex, equals(6)); // July is 6 months after January
    });

    test('should handle keyboard navigation key events', () {
      // Test keyboard event handling logic
      const leftArrowKey = 'ArrowLeft';
      const rightArrowKey = 'ArrowRight';
      const otherKey = 'Space';

      // Simulate key event handling
      String handleKeyEvent(String key) {
        if (key == leftArrowKey) {
          return 'previous_month';
        } else if (key == rightArrowKey) {
          return 'next_month';
        }
        return 'ignored';
      }

      expect(handleKeyEvent(leftArrowKey), equals('previous_month'));
      expect(handleKeyEvent(rightArrowKey), equals('next_month'));
      expect(handleKeyEvent(otherKey), equals('ignored'));
    });
  });

  // Integration tests (only run when INTEGRATION_TESTS=true)
  group('Month Navigation Integration Tests', () {
    testWidgets('should call working days API with correct month parameter',
        (WidgetTester tester) async {
      if (!const bool.fromEnvironment('INTEGRATION_TESTS',
          defaultValue: false)) {
        return;
      }

      try {
        // Test current month
        final currentResponse = await WorkingDaysService.getWorkingDays(
          mealType: 'lunch',
          monthSelected: DateTime.now(),
          customDays: [1, 2, 3, 4, 5],
        );

        expect(currentResponse.success, isTrue);
        dev.log(
            '✅ Current month API test passed: ${currentResponse.enabledDates.length} dates');

        // Test next month
        final nextMonth =
            DateTime(DateTime.now().year, DateTime.now().month + 1, 1);
        final nextResponse = await WorkingDaysService.getWorkingDays(
          mealType: 'lunch',
          monthSelected: nextMonth,
          customDays: [1, 2, 3, 4, 5],
        );

        expect(nextResponse.success, isTrue);
        dev.log(
            '✅ Next month API test passed: ${nextResponse.enabledDates.length} dates');
      } catch (e) {
        dev.log(
            '⚠️ Integration test failed (expected if API is not available): $e');
      }
    });

    testWidgets('should preserve custom days across month navigation',
        (WidgetTester tester) async {
      if (!const bool.fromEnvironment('INTEGRATION_TESTS',
          defaultValue: false)) {
        return;
      }

      try {
        final customDays = [1, 3, 5]; // Monday, Wednesday, Friday

        // Test current month with custom days
        final currentResponse = await WorkingDaysService.getWorkingDays(
          mealType: 'lunch',
          monthSelected: DateTime.now(),
          customDays: customDays,
        );

        // Test next month with same custom days
        final nextMonth =
            DateTime(DateTime.now().year, DateTime.now().month + 1, 1);
        final nextResponse = await WorkingDaysService.getWorkingDays(
          mealType: 'lunch',
          monthSelected: nextMonth,
          customDays: customDays,
        );

        expect(currentResponse.success, isTrue);
        expect(nextResponse.success, isTrue);

        dev.log('✅ Custom days preservation test passed');
        dev.log(
            '   Current month: ${currentResponse.enabledDates.length} dates');
        dev.log('   Next month: ${nextResponse.enabledDates.length} dates');
      } catch (e) {
        dev.log('⚠️ Custom days integration test failed: $e');
      }
    });
  });
}
