import 'package:flutter_test/flutter_test.dart';
import 'package:startwell/services/end_date_calculation_service.dart';

void main() {
  group('EndDateCalculationService', () {
    test('validateApiConfiguration should return true for valid config', () {
      expect(EndDateCalculationService.validateApiConfiguration(), isTrue);
    });

    test('convertSelectedWeekdaysToDays should convert boolean array correctly',
        () {
      // Test Monday, Wednesday, Friday selection
      final selectedWeekdays = [true, false, true, false, true, false, false];
      final result = EndDateCalculationService.convertSelectedWeekdaysToDays(
          selectedWeekdays);

      expect(result, equals([1, 3, 5])); // Monday=1, Wednesday=3, Friday=5
    });

    test(
        'convertSelectedWeekdaysToDays should handle Sunday correctly (Sunday=0)',
        () {
      // Test Sunday selection
      final selectedWeekdays = [false, false, false, false, false, false, true];
      final result = EndDateCalculationService.convertSelectedWeekdaysToDays(
          selectedWeekdays);

      expect(result, equals([0])); // Sunday=0
    });

    test('convertSelectedWeekdaysToDays should handle all weekdays selected',
        () {
      final selectedWeekdays = [true, true, true, true, true, true, true];
      final result = EndDateCalculationService.convertSelectedWeekdaysToDays(
          selectedWeekdays);

      expect(result, equals([1, 2, 3, 4, 5, 6, 0])); // All days with Sunday=0
    });

    test('getDefaultDeliveryDays should return Monday to Friday', () {
      final result = EndDateCalculationService.getDefaultDeliveryDays();
      expect(result, equals([1, 2, 3, 4, 5])); // Mon-Fri
    });

    test('getDefaultWeekdaySelection should return Mon-Fri selection', () {
      final result = EndDateCalculationService.getDefaultWeekdaySelection();
      expect(
          result,
          equals([
            true,
            true,
            true,
            true,
            true,
            false,
            false
          ])); // Mon-Fri selected
    });

    test('convertSelectedWeekdaysToDays should handle no weekdays selected',
        () {
      final selectedWeekdays = [
        false,
        false,
        false,
        false,
        false,
        false,
        false
      ];
      final result = EndDateCalculationService.convertSelectedWeekdaysToDays(
          selectedWeekdays);

      expect(result, isEmpty);
    });

    test('convertSelectedWeekdaysToDays should handle partial array', () {
      final selectedWeekdays = [true, false, true]; // Only first 3 days
      final result = EndDateCalculationService.convertSelectedWeekdaysToDays(
          selectedWeekdays);

      expect(result, equals([1, 3])); // Monday and Wednesday
    });

    // Note: API integration tests would require a running backend
    // For now, we'll test the service configuration and utility methods

    group('EndDateCalculationResponse', () {
      test('should create response correctly', () {
        final startDate = DateTime(2025, 8, 15);
        final endDate = DateTime(2025, 9, 15);
        final response = EndDateCalculationResponse(
          success: true,
          message: 'Test message',
          endDate: endDate,
          startDate: startDate,
          planId: 1,
          selectedDays: [1, 2, 3, 4, 5],
        );

        expect(response.success, isTrue);
        expect(response.message, equals('Test message'));
        expect(response.endDate, equals(endDate));
        expect(response.startDate, equals(startDate));
        expect(response.planId, equals(1));
        expect(response.selectedDays, equals([1, 2, 3, 4, 5]));
      });

      test('should convert to JSON correctly', () {
        final startDate = DateTime(2025, 8, 15);
        final endDate = DateTime(2025, 9, 15);
        final response = EndDateCalculationResponse(
          success: true,
          message: 'Test message',
          endDate: endDate,
          startDate: startDate,
          planId: 1,
          selectedDays: [1, 2, 3, 4, 5],
        );

        final json = response.toJson();

        expect(json['success'], isTrue);
        expect(json['message'], equals('Test message'));
        expect(json['end_date'], equals('2025-09-15'));
        expect(json['start_date'], equals('2025-08-15'));
        expect(json['plan_id'], equals(1));
        expect(json['selected_days'], equals([1, 2, 3, 4, 5]));
      });

      test('should create from JSON correctly', () {
        final json = {
          'success': true,
          'message': 'Test message',
          'end_date': '2025-09-15',
          'start_date': '2025-08-15',
          'plan_id': 1,
          'selected_days': [1, 2, 3, 4, 5],
        };

        final response = EndDateCalculationResponse.fromJson(json);

        expect(response.success, isTrue);
        expect(response.message, equals('Test message'));
        expect(response.endDate, equals(DateTime(2025, 9, 15)));
        expect(response.startDate, equals(DateTime(2025, 8, 15)));
        expect(response.planId, equals(1));
        expect(response.selectedDays, equals([1, 2, 3, 4, 5]));
      });

      test('toString should format correctly', () {
        final startDate = DateTime(2025, 8, 15);
        final endDate = DateTime(2025, 9, 15);
        final response = EndDateCalculationResponse(
          success: true,
          message: 'Test message',
          endDate: endDate,
          startDate: startDate,
          planId: 1,
          selectedDays: [1, 2, 3, 4, 5],
        );

        final stringRepresentation = response.toString();

        expect(stringRepresentation, contains('success: true'));
        expect(stringRepresentation, contains('message: Test message'));
        expect(stringRepresentation, contains('startDate: 2025-08-15'));
        expect(stringRepresentation, contains('endDate: 2025-09-15'));
        expect(stringRepresentation, contains('planId: 1'));
        expect(stringRepresentation, contains('selectedDays: [1, 2, 3, 4, 5]'));
      });
    });
  });
}
